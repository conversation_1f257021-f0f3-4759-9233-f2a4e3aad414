/**
 * Message Reaction Add Event Handler
 * Handles Discord message reaction events for rewards and milestone tracking
 */

import { MessageReaction, User, PartialMessageReaction, PartialUser } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';

/**
 * Message reaction add event handler
 */
export class MessageReactionAddEventHandler extends BaseEventHandler {
  public readonly name = 'messageReactionAdd';

  constructor(app: IApplicationContext) {
    super(app);
  }

  /**
   * Execute message reaction add event
   */
  async execute(reaction: MessageReaction | PartialMessageReaction, user: User | PartialUser): Promise<void> {
    try {
      // Handle partial reactions and users
      const fullReaction = await this.ensureFullReaction(reaction);
      const fullUser = await this.ensureFullUser(user);

      if (!fullReaction || !fullUser) {
        return;
      }

      // Skip bot users
      if (fullUser.bot) return;

      // Process reaction rewards
      if (this.isFeatureEnabled('REACTION_REWARDS')) {
        await this.processReactionReward(fullReaction, fullUser);
      }

      // Track reaction activity for milestones
      if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
        await this.trackReactionActivity(fullReaction, fullUser);
      }

    } catch (error) {
      this.handleError(error, {
        messageId: reaction.message.id,
        channelId: reaction.message.channel.id,
        guildId: reaction.message.guild?.id,
        userId: user.id,
        emoji: reaction.emoji.name,
      });
    }
  }

  /**
   * Ensure reaction is fully fetched
   */
  private async ensureFullReaction(reaction: MessageReaction | PartialMessageReaction): Promise<MessageReaction | null> {
    if (reaction.partial) {
      try {
        return await reaction.fetch();
      } catch (error) {
        this.logger.error('[MessageReactionAdd] Failed to fetch reaction', { error });
        return null;
      }
    }
    return reaction as MessageReaction;
  }

  /**
   * Ensure user is fully fetched
   */
  private async ensureFullUser(user: User | PartialUser): Promise<User | null> {
    if (user.partial) {
      try {
        return await user.fetch();
      } catch (error) {
        this.logger.error('[MessageReactionAdd] Failed to fetch user', { error });
        return null;
      }
    }
    return user as User;
  }

  /**
   * Process reaction reward
   */
  private async processReactionReward(reaction: MessageReaction, user: User): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { processReactionReward } = await import('../services/reactionRewardsService');

      await processReactionReward(reaction, user);
    } catch (error) {
      this.logger.error('[MessageReactionAdd] Error processing reaction reward', {
        error,
        messageId: reaction.message.id,
        userId: user.id,
        emoji: reaction.emoji.name,
      });
    }
  }

  /**
   * Track reaction activity for milestones
   */
  private async trackReactionActivity(reaction: MessageReaction, user: User): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { checkAndProcessMilestones } = await import('../services/milestoneService');

      const milestoneResults = await checkAndProcessMilestones(
        this.app.client,
        user.id,
        reaction.message.guild?.id || '',
        'reaction',
        {
          channelId: reaction.message.channel.id,
          emoji: reaction.emoji,
          timestamp: new Date()
        }
      );

      if (milestoneResults.length > 0) {
        this.logger.info(`[MessageReactionAdd] User ${user.tag} achieved ${milestoneResults.length} milestone(s) from reaction activity`);
      }
    } catch (error) {
      this.logger.error('[MessageReactionAdd] Error processing reaction milestones', {
        error,
        messageId: reaction.message.id,
        userId: user.id,
        guildId: reaction.message.guild?.id,
      });
    }
  }
}
