/**
 * Base Service Class
 * Abstract base class for all application services
 */

import { IService, ILogger, IApplicationContext } from '../../core/interfaces';
import { createLogger } from '../../core/logger';

/**
 * Abstract base service class
 */
export abstract class BaseService implements IService {
  public abstract readonly name: string;
  
  protected logger: ILogger;
  protected app?: IApplicationContext;

  constructor(app?: IApplicationContext) {
    this.app = app;
    this.logger = createLogger(`service:${this.constructor.name}`);
  }

  /**
   * Initialize the service
   */
  async initialize?(): Promise<void> {
    this.logger.info(`[${this.name}] Service initializing...`);
    await this.onInitialize();
    this.logger.info(`[${this.name}] Service initialized`);
  }

  /**
   * Shutdown the service
   */
  async shutdown?(): Promise<void> {
    this.logger.info(`[${this.name}] Service shutting down...`);
    await this.onShutdown();
    this.logger.info(`[${this.name}] Service shutdown complete`);
  }

  /**
   * Override in subclasses for initialization logic
   */
  protected async onInitialize(): Promise<void> {
    // Default implementation - override in subclasses
  }

  /**
   * Override in subclasses for shutdown logic
   */
  protected async onShutdown(): Promise<void> {
    // Default implementation - override in subclasses
  }

  /**
   * Check if a feature is enabled
   */
  protected isFeatureEnabled(featureName: string): boolean {
    // Import here to avoid circular dependencies
    const { isFeatureActive } = require('../../config');
    return isFeatureActive(featureName);
  }

  /**
   * Get another service from the application context
   */
  protected getService<T extends IService>(serviceName: string): T {
    if (!this.app) {
      throw new Error(`Cannot get service ${serviceName}: No application context available`);
    }
    return this.app.getService<T>(serviceName);
  }

  /**
   * Handle service errors with consistent logging
   */
  protected handleError(error: any, context?: any): void {
    this.logger.error(`[${this.name}] Service error`, {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
      serviceName: this.name,
    });
  }

  /**
   * Log service operations
   */
  protected logOperation(operation: string, details?: any): void {
    this.logger.debug(`[${this.name}] ${operation}`, details);
  }

  /**
   * Validate required dependencies
   */
  protected validateDependencies(dependencies: string[]): void {
    if (!this.app) {
      throw new Error(`Cannot validate dependencies: No application context available`);
    }

    for (const dependency of dependencies) {
      try {
        this.app.getService(dependency);
      } catch (error) {
        throw new Error(`Required dependency not available: ${dependency}`);
      }
    }
  }

  /**
   * Create a child logger with additional context
   */
  protected createChildLogger(context: string): ILogger {
    return createLogger(`${this.name}:${context}`);
  }
}

/**
 * Service registry for managing service instances
 */
export class ServiceRegistry {
  private services = new Map<string, IService>();
  private logger: ILogger;

  constructor() {
    this.logger = createLogger('service-registry');
  }

  /**
   * Register a service
   */
  register(service: IService): void {
    if (this.services.has(service.name)) {
      this.logger.warn(`[ServiceRegistry] Service already registered: ${service.name}`);
      return;
    }

    this.services.set(service.name, service);
    this.logger.debug(`[ServiceRegistry] Registered service: ${service.name}`);
  }

  /**
   * Get a service by name
   */
  get<T extends IService>(name: string): T {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service not found: ${name}`);
    }
    return service as T;
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered services
   */
  getAll(): IService[] {
    return Array.from(this.services.values());
  }

  /**
   * Unregister a service
   */
  unregister(name: string): boolean {
    const removed = this.services.delete(name);
    if (removed) {
      this.logger.debug(`[ServiceRegistry] Unregistered service: ${name}`);
    }
    return removed;
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
    this.logger.debug('[ServiceRegistry] Cleared all services');
  }
}

/**
 * Global service registry instance
 */
export const serviceRegistry = new ServiceRegistry();
