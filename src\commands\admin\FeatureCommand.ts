/**
 * Feature Management Command
 * Admin command for managing runtime feature toggles
 */

import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createAdminEmbed, createSuccessEmbed, createErrorEmbed, EMOJIS } from '../../utils/embedBuilder';
import { runtimeConfig, FEATURE_REGISTRY } from '../../config';
import { ValidationError } from '../../utils/errorHandler';

/**
 * Feature management command implementation
 */
export class FeatureCommand extends BaseCommand {
  constructor() {
    super({
      name: 'feature',
      description: 'Manage runtime feature toggles (admin only)',
      category: CommandCategory.ADMIN,
      adminOnly: true,
      requiredPermissions: ['Administrator'],
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addSubcommand(subcommand =>
        subcommand
          .setName('list')
          .setDescription('List all features and their status'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('enable')
          .setDescription('Enable a feature')
          .addStringOption(option =>
            option.setName('feature')
              .setDescription('Feature name to enable')
              .setRequired(true)
              .addChoices(...this.getFeatureChoices()))
          .addStringOption(option =>
            option.setName('reason')
              .setDescription('Reason for enabling the feature')
              .setRequired(false)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('disable')
          .setDescription('Disable a feature')
          .addStringOption(option =>
            option.setName('feature')
              .setDescription('Feature name to disable')
              .setRequired(true)
              .addChoices(...this.getFeatureChoices()))
          .addStringOption(option =>
            option.setName('reason')
              .setDescription('Reason for disabling the feature')
              .setRequired(false))
          .addIntegerOption(option =>
            option.setName('duration')
              .setDescription('Duration in minutes (optional)')
              .setRequired(false)
              .setMinValue(1)
              .setMaxValue(10080))) // Max 1 week
      .addSubcommand(subcommand =>
        subcommand
          .setName('reset')
          .setDescription('Reset a feature to its default state')
          .addStringOption(option =>
            option.setName('feature')
              .setDescription('Feature name to reset')
              .setRequired(true)
              .addChoices(...this.getFeatureChoices())))
      .addSubcommand(subcommand =>
        subcommand
          .setName('status')
          .setDescription('Get detailed status of a specific feature')
          .addStringOption(option =>
            option.setName('feature')
              .setDescription('Feature name to check')
              .setRequired(true)
              .addChoices(...this.getFeatureChoices())))
      .addSubcommand(subcommand =>
        subcommand
          .setName('export')
          .setDescription('Export current feature configuration'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('stats')
          .setDescription('Show feature management statistics'));
  }

  /**
   * Execute the feature command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'list':
        await this.handleList(context);
        break;
      case 'enable':
        await this.handleEnable(context);
        break;
      case 'disable':
        await this.handleDisable(context);
        break;
      case 'reset':
        await this.handleReset(context);
        break;
      case 'status':
        await this.handleStatus(context);
        break;
      case 'export':
        await this.handleExport(context);
        break;
      case 'stats':
        await this.handleStats(context);
        break;
      default:
        throw new ValidationError(`Unknown subcommand: ${subcommand}`);
    }
  }

  /**
   * Handle list subcommand
   */
  private async handleList(context: CommandContext): Promise<void> {
    const { interaction } = context;

    const embed = createAdminEmbed('Feature Status Overview')
      .setDescription(`${EMOJIS.ADMIN.TOOLS} **Runtime Feature Management**\n\nCurrent status of all features:`);

    const features = Object.entries(FEATURE_REGISTRY);
    const overrides = runtimeConfig.getFeatureOverrides();

    for (const [featureName, config] of features) {
      const isEnabled = runtimeConfig.isFeatureEnabled(featureName);
      const override = overrides.get(featureName);
      
      let status = isEnabled ? '🟢 Enabled' : '🔴 Disabled';
      let details = config.description;

      if (override) {
        status += ' (Override)';
        details += `\n*Override: ${override.reason}*`;
        if (override.expiresAt) {
          details += `\n*Expires: <t:${Math.floor(override.expiresAt.getTime() / 1000)}:R>*`;
        }
      }

      embed.addFields({
        name: `${status} ${featureName}`,
        value: details,
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  /**
   * Handle enable subcommand
   */
  private async handleEnable(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const featureName = interaction.options.getString('feature', true);
    const reason = interaction.options.getString('reason') || 'Manual enable by admin';

    if (!FEATURE_REGISTRY[featureName]) {
      throw new ValidationError(`Feature not found: ${featureName}`);
    }

    runtimeConfig.setFeatureOverride(
      featureName,
      true,
      reason,
      `admin:${interaction.user.username}`
    );

    const embed = createSuccessEmbed('Feature Enabled')
      .setDescription(
        `${EMOJIS.SUCCESS.CHECK} **${featureName}** has been enabled\n\n` +
        `**Reason:** ${reason}\n` +
        `**Admin:** ${interaction.user.displayName}`
      );

    await interaction.reply({ embeds: [embed], ephemeral: false });

    this.logger.info(`Feature enabled by admin`, {
      featureName,
      reason,
      adminId: interaction.user.id,
      adminUsername: interaction.user.username,
    });
  }

  /**
   * Handle disable subcommand
   */
  private async handleDisable(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const featureName = interaction.options.getString('feature', true);
    const reason = interaction.options.getString('reason') || 'Manual disable by admin';
    const duration = interaction.options.getInteger('duration');

    if (!FEATURE_REGISTRY[featureName]) {
      throw new ValidationError(`Feature not found: ${featureName}`);
    }

    const expiresAt = duration ? new Date(Date.now() + duration * 60 * 1000) : undefined;

    runtimeConfig.setFeatureOverride(
      featureName,
      false,
      reason,
      `admin:${interaction.user.username}`,
      expiresAt
    );

    const embed = createSuccessEmbed('Feature Disabled')
      .setDescription(
        `${EMOJIS.ADMIN.WARNING} **${featureName}** has been disabled\n\n` +
        `**Reason:** ${reason}\n` +
        `**Admin:** ${interaction.user.displayName}` +
        (expiresAt ? `\n**Expires:** <t:${Math.floor(expiresAt.getTime() / 1000)}:R>` : '')
      );

    await interaction.reply({ embeds: [embed], ephemeral: false });

    this.logger.info(`Feature disabled by admin`, {
      featureName,
      reason,
      duration,
      expiresAt,
      adminId: interaction.user.id,
      adminUsername: interaction.user.username,
    });
  }

  /**
   * Handle reset subcommand
   */
  private async handleReset(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const featureName = interaction.options.getString('feature', true);

    if (!FEATURE_REGISTRY[featureName]) {
      throw new ValidationError(`Feature not found: ${featureName}`);
    }

    const removed = runtimeConfig.removeFeatureOverride(featureName, `admin:${interaction.user.username}`);

    if (!removed) {
      throw new ValidationError(`No override found for feature: ${featureName}`);
    }

    const defaultState = FEATURE_REGISTRY[featureName].enabled;
    const embed = createSuccessEmbed('Feature Reset')
      .setDescription(
        `${EMOJIS.SUCCESS.REFRESH} **${featureName}** has been reset to default state\n\n` +
        `**Default State:** ${defaultState ? 'Enabled' : 'Disabled'}\n` +
        `**Admin:** ${interaction.user.displayName}`
      );

    await interaction.reply({ embeds: [embed], ephemeral: false });

    this.logger.info(`Feature reset by admin`, {
      featureName,
      defaultState,
      adminId: interaction.user.id,
      adminUsername: interaction.user.username,
    });
  }

  /**
   * Handle status subcommand
   */
  private async handleStatus(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const featureName = interaction.options.getString('feature', true);

    if (!FEATURE_REGISTRY[featureName]) {
      throw new ValidationError(`Feature not found: ${featureName}`);
    }

    const config = runtimeConfig.getFeatureConfig(featureName);
    const isEnabled = runtimeConfig.isFeatureEnabled(featureName);

    const embed = createAdminEmbed(`Feature Status: ${featureName}`)
      .setDescription(config.description)
      .addFields(
        {
          name: 'Current Status',
          value: isEnabled ? '🟢 Enabled' : '🔴 Disabled',
          inline: true
        },
        {
          name: 'Default State',
          value: FEATURE_REGISTRY[featureName].enabled ? '🟢 Enabled' : '🔴 Disabled',
          inline: true
        }
      );

    if (config.dependencies && config.dependencies.length > 0) {
      embed.addFields({
        name: 'Dependencies',
        value: config.dependencies.join(', '),
        inline: false
      });
    }

    if (config.override) {
      embed.addFields(
        {
          name: 'Override Active',
          value: `**Reason:** ${config.override.reason}\n**Source:** ${config.override.source}\n**Set:** <t:${Math.floor(config.override.timestamp.getTime() / 1000)}:R>`,
          inline: false
        }
      );

      if (config.override.expiresAt) {
        embed.addFields({
          name: 'Expires',
          value: `<t:${Math.floor(config.override.expiresAt.getTime() / 1000)}:R>`,
          inline: true
        });
      }
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  /**
   * Handle export subcommand
   */
  private async handleExport(context: CommandContext): Promise<void> {
    const { interaction } = context;

    const config = runtimeConfig.exportConfig();
    const configJson = JSON.stringify(config, null, 2);

    const embed = createAdminEmbed('Feature Configuration Export')
      .setDescription(
        `${EMOJIS.ADMIN.TOOLS} **Current Configuration**\n\n` +
        `**Exported:** <t:${Math.floor(Date.now() / 1000)}:f>\n` +
        `**Feature Overrides:** ${Object.keys(config.featureOverrides).length}\n` +
        `**Config Overrides:** ${Object.keys(config.configOverrides).length}`
      );

    // If config is small enough, include it in the embed
    if (configJson.length < 1000) {
      embed.addFields({
        name: 'Configuration',
        value: `\`\`\`json\n${configJson}\`\`\``,
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });

    // Also send as file if it's large
    if (configJson.length >= 1000) {
      const buffer = Buffer.from(configJson, 'utf8');
      await interaction.followUp({
        content: 'Configuration file:',
        files: [{
          attachment: buffer,
          name: `feature-config-${Date.now()}.json`
        }],
        ephemeral: true
      });
    }
  }

  /**
   * Handle stats subcommand
   */
  private async handleStats(context: CommandContext): Promise<void> {
    const { interaction } = context;

    const stats = runtimeConfig.getStats();

    const embed = createAdminEmbed('Feature Management Statistics')
      .addFields(
        {
          name: 'Total Features',
          value: stats.totalFeatures.toString(),
          inline: true
        },
        {
          name: 'Enabled Features',
          value: stats.enabledFeatures.toString(),
          inline: true
        },
        {
          name: 'Active Overrides',
          value: stats.featureOverrides.toString(),
          inline: true
        },
        {
          name: 'Config Overrides',
          value: stats.configOverrides.toString(),
          inline: true
        }
      );

    if (stats.overriddenFeatures.length > 0) {
      embed.addFields({
        name: 'Overridden Features',
        value: stats.overriddenFeatures.join(', '),
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  /**
   * Get feature choices for command options
   */
  private getFeatureChoices(): Array<{ name: string; value: string }> {
    return Object.keys(FEATURE_REGISTRY).map(featureName => ({
      name: featureName,
      value: featureName
    }));
  }
}
