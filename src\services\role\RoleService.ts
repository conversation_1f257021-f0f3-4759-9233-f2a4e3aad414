/**
 * Role Service
 * Refactored role assignment service with improved architecture
 */

import { Client, Guild, GuildMember } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { IRoleService, IApplicationContext, RoleAssignmentResult, UserRoleInfo } from '../../core/interfaces';
import { requireFeature } from '../../config/features';
import { RoleForSale } from '../../models/User';
import { createSuccessEmbed, addUserInfo, formatCoins, EMOJIS } from '../../utils/embedBuilder';
import { DatabaseError } from '../../utils/errorHandler';

/**
 * Role service implementation
 */
export class RoleService extends BaseService implements IRoleService {
  public readonly name = 'RoleService';

  constructor(app: IApplicationContext) {
    super(app);
  }

  /**
   * Initialize the role service
   */
  protected async onInitialize(): Promise<void> {
    if (!this.isFeatureEnabled('ROLE_AUTOMATION')) {
      throw new Error('Role automation is not enabled');
    }
    
    this.logger.info('[RoleService] Role automation system initialized');
  }

  /**
   * Check and assign roles based on user balance
   */
  @requireFeature('ROLE_AUTOMATION')
  async checkAndAssignRoles(
    client: Client,
    discordId: string,
    guildId: string,
    balance: number
  ): Promise<RoleAssignmentResult | null> {
    try {
      this.logOperation('Checking role assignments', { discordId, guildId, balance });

      const guild = await client.guilds.fetch(guildId);
      if (!guild) {
        throw new DatabaseError(`Guild not found: ${guildId}`);
      }

      const member = await guild.members.fetch(discordId);
      if (!member) {
        throw new DatabaseError(`Member not found: ${discordId}`);
      }

      // Get available roles for sale in this guild
      const rolesForSale = await RoleForSale.find({ guildId }).sort({ price: 1 });
      if (rolesForSale.length === 0) {
        return null;
      }

      // Find roles the user can afford but doesn't have
      const rolesToAssign = [];
      for (const roleInfo of rolesForSale) {
        // Check if user can afford this role
        if (balance >= roleInfo.price) {
          // Check if user doesn't already have this role
          if (!member.roles.cache.has(roleInfo.roleId)) {
            // Verify the role still exists in the guild
            const discordRole = await guild.roles.fetch(roleInfo.roleId);
            if (discordRole) {
              rolesToAssign.push({
                ...roleInfo.toObject(),
                discordRole
              });
            }
          }
        }
      }

      // If no roles to assign, return null
      if (rolesToAssign.length === 0) {
        return null;
      }

      // Assign all qualifying roles
      const assignedRoles = [];
      for (const roleInfo of rolesToAssign) {
        try {
          await member.roles.add(roleInfo.discordRole);
          
          // Record the achievement unlock
          const economyService = this.getService('EconomyService');
          await economyService.adjustBalance(
            discordId,
            0,
            'role_achievement',
            `Achievement unlocked: ${roleInfo.roleName} (Required: ${roleInfo.price} PLC)`,
            client,
            guildId
          );

          assignedRoles.push({
            roleId: roleInfo.roleId,
            roleName: roleInfo.roleName,
            price: roleInfo.price,
            description: roleInfo.description
          });

          this.logger.info(`Assigned role ${roleInfo.roleName} to user ${discordId} in guild ${guildId}`);
        } catch (error) {
          this.handleError(error, { 
            operation: 'role_assignment',
            roleId: roleInfo.roleId,
            discordId,
            guildId 
          });
        }
      }

      if (assignedRoles.length === 0) {
        return null;
      }

      return {
        rolesAssigned: assignedRoles,
        member,
        newBalance: balance
      };

    } catch (error) {
      this.handleError(error, { discordId, guildId, balance });
      throw new DatabaseError(`Failed to check and assign roles: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user achievement roles information
   */
  @requireFeature('ROLE_AUTOMATION')
  async getUserAchievementRoles(member: GuildMember): Promise<UserRoleInfo> {
    try {
      this.logOperation('Getting user achievement roles', { 
        userId: member.user.id, 
        guildId: member.guild.id 
      });

      // Get user's current balance
      const economyService = this.getService('EconomyService');
      const userBalance = await economyService.getBalance(member.user.id);

      // Get all roles for sale in this guild
      const rolesForSale = await RoleForSale.find({ guildId: member.guild.id }).sort({ price: 1 });

      const currentRoles = [];
      const availableRoles = [];

      for (const roleInfo of rolesForSale) {
        // Check if the role still exists in Discord
        const discordRole = await member.guild.roles.fetch(roleInfo.roleId).catch(() => null);
        if (!discordRole) {
          continue; // Skip roles that no longer exist
        }

        const roleData = {
          roleId: roleInfo.roleId,
          roleName: roleInfo.roleName,
          price: roleInfo.price,
          description: roleInfo.description
        };

        if (member.roles.cache.has(roleInfo.roleId)) {
          // User has this role
          currentRoles.push(roleData);
        } else {
          // User doesn't have this role
          availableRoles.push({
            ...roleData,
            canAfford: userBalance >= roleInfo.price
          });
        }
      }

      return {
        currentRoles,
        availableRoles,
        userBalance
      };

    } catch (error) {
      this.handleError(error, { 
        userId: member.user.id, 
        guildId: member.guild.id 
      });
      throw new DatabaseError(`Failed to get user achievement roles: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Send role achievement notifications
 */
export async function sendRoleAchievementNotifications(
  result: RoleAssignmentResult,
  client: Client
): Promise<void> {
  try {
    if (result.rolesAssigned.length === 0) {
      return;
    }

    // Create achievement notification embed
    const embed = createSuccessEmbed('🎉 Achievement Unlocked!')
      .setDescription(
        `Congratulations! You've automatically unlocked ${result.rolesAssigned.length} new role achievement${result.rolesAssigned.length > 1 ? 's' : ''}!`
      );

    // Add role information
    for (const role of result.rolesAssigned) {
      embed.addFields({
        name: `${EMOJIS.SUCCESS.STAR} ${role.roleName}`,
        value: `Required: ${formatCoins(role.price)}\n${role.description || 'No description available'}`,
        inline: true
      });
    }

    // Add user info
    addUserInfo(embed, result.member.user, result.newBalance);

    // Send DM to user
    try {
      await result.member.send({ embeds: [embed] });
    } catch (error) {
      // If DM fails, we could optionally send to a channel
      console.log(`Failed to send role achievement DM to ${result.member.displayName}: ${error}`);
    }

  } catch (error) {
    console.error('Error sending role achievement notifications:', error);
  }
}

// Legacy exports for backward compatibility
export { checkAndAssignRoles, getUserAchievementRoles } from '../roleAssignmentService';
