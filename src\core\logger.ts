/**
 * Centralized Logging System
 * Winston-based logger with structured logging and multiple transports
 */

import winston from 'winston';
import { ILogger, LoggingConfig } from './interfaces';
import { getLoggingConfig, isDevelopment, isProduction } from '../config';
import { LOGGING } from '../config/constants';

/**
 * Custom log format for development
 */
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, category, ...meta }) => {
    const categoryStr = category ? `[${category}]` : '';
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    return `${timestamp} ${level} ${categoryStr} ${message}${metaStr}`;
  })
);

/**
 * Custom log format for production
 */
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

/**
 * Logger implementation using Winston
 */
export class Logger implements ILogger {
  private winston: winston.Logger;
  private config: LoggingConfig;

  constructor(config?: LoggingConfig) {
    this.config = config || getLoggingConfig();
    this.winston = this.createLogger();
  }

  /**
   * Create Winston logger instance
   */
  private createLogger(): winston.Logger {
    const transports: winston.transport[] = [];

    // Console transport
    if (this.config.console) {
      transports.push(
        new winston.transports.Console({
          level: this.config.level,
          format: isDevelopment() ? developmentFormat : productionFormat,
        })
      );
    }

    // File transport
    if (this.config.file && this.config.filePath) {
      transports.push(
        new winston.transports.File({
          filename: this.config.filePath,
          level: this.config.level,
          format: productionFormat,
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true,
        })
      );
    }

    return winston.createLogger({
      level: this.config.level,
      format: this.config.format === 'json' ? productionFormat : developmentFormat,
      transports,
      exitOnError: false,
    });
  }

  /**
   * Log error message
   */
  error(message: string, meta?: any): void {
    this.winston.error(message, this.formatMeta(meta));
  }

  /**
   * Log warning message
   */
  warn(message: string, meta?: any): void {
    this.winston.warn(message, this.formatMeta(meta));
  }

  /**
   * Log info message
   */
  info(message: string, meta?: any): void {
    this.winston.info(message, this.formatMeta(meta));
  }

  /**
   * Log debug message
   */
  debug(message: string, meta?: any): void {
    this.winston.debug(message, this.formatMeta(meta));
  }

  /**
   * Log with specific category
   */
  logWithCategory(level: string, category: string, message: string, meta?: any): void {
    this.winston.log(level, message, { ...this.formatMeta(meta), category });
  }

  /**
   * Format metadata for logging
   */
  private formatMeta(meta?: any): any {
    if (!meta) return {};

    // Handle Error objects
    if (meta instanceof Error) {
      return {
        error: {
          name: meta.name,
          message: meta.message,
          stack: meta.stack,
        },
      };
    }

    // Handle objects with circular references
    try {
      JSON.stringify(meta);
      return meta;
    } catch (error) {
      return { serializedMeta: String(meta) };
    }
  }

  /**
   * Create child logger with category
   */
  child(category: string): CategoryLogger {
    return new CategoryLogger(this, category);
  }

  /**
   * Get Winston logger instance (for advanced usage)
   */
  getWinstonLogger(): winston.Logger {
    return this.winston;
  }

  /**
   * Update log level
   */
  setLevel(level: string): void {
    this.winston.level = level;
    this.winston.transports.forEach(transport => {
      transport.level = level;
    });
  }

  /**
   * Close logger and cleanup resources
   */
  close(): void {
    this.winston.close();
  }
}

/**
 * Category-specific logger
 */
export class CategoryLogger implements ILogger {
  constructor(private parent: Logger, private category: string) {}

  error(message: string, meta?: any): void {
    this.parent.logWithCategory('error', this.category, message, meta);
  }

  warn(message: string, meta?: any): void {
    this.parent.logWithCategory('warn', this.category, message, meta);
  }

  info(message: string, meta?: any): void {
    this.parent.logWithCategory('info', this.category, message, meta);
  }

  debug(message: string, meta?: any): void {
    this.parent.logWithCategory('debug', this.category, message, meta);
  }
}

/**
 * Global logger instance
 */
let globalLogger: Logger | null = null;

/**
 * Get or create global logger
 */
export function getLogger(): Logger {
  if (!globalLogger) {
    globalLogger = new Logger();
  }
  return globalLogger;
}

/**
 * Create logger with category
 */
export function createLogger(category: string): CategoryLogger {
  return getLogger().child(category);
}

/**
 * Logger factory for different categories
 */
export const loggers = {
  economy: () => createLogger(LOGGING.CATEGORIES.ECONOMY),
  milestone: () => createLogger(LOGGING.CATEGORIES.MILESTONE),
  dynasty: () => createLogger(LOGGING.CATEGORIES.DYNASTY),
  admin: () => createLogger(LOGGING.CATEGORIES.ADMIN),
  database: () => createLogger(LOGGING.CATEGORIES.DATABASE),
  discord: () => createLogger(LOGGING.CATEGORIES.DISCORD),
};

/**
 * Shutdown logger
 */
export function shutdownLogger(): void {
  if (globalLogger) {
    globalLogger.close();
    globalLogger = null;
  }
}

export default Logger;
