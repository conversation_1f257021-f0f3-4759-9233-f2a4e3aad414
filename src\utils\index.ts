/**
 * Utils Module Index
 * Centralized exports for all utility modules
 */

// Enhanced utility modules
export * from './validation/ValidationUtils';
export * from './formatting/FormatUtils';
export * from './discord/DiscordUtils';

// Legacy utilities (maintained for compatibility)
export * from './embedBuilder';
export * from './errorHandler';
export * from './roleResolver';

// Shorthand exports for common utilities
export { ValidationUtils } from './validation/ValidationUtils';
export { FormatUtils } from './formatting/FormatUtils';
export { DiscordUtils } from './discord/DiscordUtils';

// Legacy shorthand exports
export { formatCoins, createSuccessEmbed, createErrorEmbed, createEconomyEmbed } from './embedBuilder';
export { withErrorHandler, ValidationError, DatabaseError } from './errorHandler';
export { resolveRole } from './roleResolver';
