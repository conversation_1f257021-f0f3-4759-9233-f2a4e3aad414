/**
 * Guild Member Remove Event Handler
 * Handles Discord guild member leave events for user data cleanup
 */

import { GuildMember, PartialGuildMember } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';

/**
 * Guild member remove event handler
 */
export class GuildMemberRemoveEventHandler extends BaseEventHandler {
  public readonly name = 'guildMemberRemove';

  constructor(app: IApplicationContext) {
    super(app);
  }

  /**
   * Execute guild member remove event
   */
  async execute(member: GuildMember | PartialGuildMember): Promise<void> {
    try {
      // Handle partial members
      const fullMember = await this.ensureFullMember(member);
      
      const userId = member.user?.id;
      const guildName = member.guild?.name || 'Unknown Guild';
      const displayName = fullMember?.displayName || member.user?.username || 'Unknown User';

      if (!userId) {
        this.logger.error('[GuildMemberRemove] No user ID available for member who left');
        return;
      }

      this.logExecution(`Member left: ${displayName}`, {
        userId,
        guildId: member.guild?.id,
        guildName,
      });

      // Process user data cleanup
      if (this.isFeatureEnabled('USER_CLEANUP')) {
        await this.processUserCleanup(member as GuildMember, userId, displayName, guildName);
      }

    } catch (error) {
      this.handleError(error, {
        userId: member.user?.id,
        guildId: member.guild?.id,
        displayName: member.displayName || member.user?.username,
      });
    }
  }

  /**
   * Ensure member is fully fetched
   */
  private async ensureFullMember(member: GuildMember | PartialGuildMember): Promise<GuildMember | null> {
    if (member.partial) {
      try {
        return await member.fetch();
      } catch (error) {
        this.logger.error('[GuildMemberRemove] Failed to fetch member data', { error });
        // Continue with cleanup using available data
        return null;
      }
    }
    return member as GuildMember;
  }

  /**
   * Process user data cleanup
   */
  private async processUserCleanup(
    member: GuildMember,
    userId: string,
    displayName: string,
    guildName: string
  ): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { UserCleanupService } = await import('../services/userCleanupService');

      // Check if we have user data before attempting cleanup
      const userData = await UserCleanupService.checkUserData(userId);
      const hasData = userData.hasUserRecord || userData.transactionCount > 0 || userData.reactionRewardCount > 0;

      if (!hasData) {
        this.logger.info(`[GuildMemberRemove] No data found for ${displayName} (${userId}) who left ${guildName}, skipping cleanup`);
        return;
      }

      this.logger.info(`[GuildMemberRemove] User ${displayName} left ${guildName} - found data: ${userData.hasUserRecord ? 'balance' : ''} ${userData.transactionCount > 0 ? `${userData.transactionCount} transactions` : ''} ${userData.reactionRewardCount > 0 ? `${userData.reactionRewardCount} reaction rewards` : ''}`.trim());

      // Perform cleanup
      const cleanupResult = await UserCleanupService.cleanupUserData(member);

      if (cleanupResult.success) {
        const removedItems = [];
        if (cleanupResult.userDataRemoved) removedItems.push('user balance');
        if (cleanupResult.transactionsRemoved > 0) removedItems.push(`${cleanupResult.transactionsRemoved} transactions`);
        if (cleanupResult.reactionRewardsRemoved > 0) removedItems.push(`${cleanupResult.reactionRewardsRemoved} reaction rewards`);

        this.logger.info(`[GuildMemberRemove] Successfully cleaned up data for ${displayName}: ${removedItems.join(', ')} (${cleanupResult.timeTaken}ms)`);
      } else {
        this.logger.error(`[GuildMemberRemove] Failed to clean up data for ${displayName}`, {
          errors: cleanupResult.errors,
        });
      }
    } catch (error) {
      this.logger.error('[GuildMemberRemove] Error processing user cleanup', {
        error,
        userId,
        displayName,
        guildName,
      });
    }
  }
}
