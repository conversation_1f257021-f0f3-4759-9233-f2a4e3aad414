/**
 * Configuration Module Index
 * Centralized exports for all configuration modules
 */

// Environment configuration
export { ENV, isDevelopment, isProduction, isTest, getDatabaseConfig, getDiscordConfig, getLoggingConfig } from './environment';

// Constants
export { ECONOMY, MILESTONES, DYNASTY, DISCORD, DATABASE, ERROR_HANDLING, LOGGING, FEATURES, VALIDATION, SCHEDULES } from './constants';

// Feature management
export { featureManager, isFeatureActive, requiresAdminPermission, isGuildSpecificFeature, requireFeature, FEATURE_REGISTRY } from './features';

// Type exports
export type { FeatureConfig } from './features';

/**
 * Configuration validation on module load
 */
import { ENV } from './environment';
import { featureManager } from './features';

// Validate configuration on startup
console.log('[Config] Environment:', ENV.NODE_ENV);
console.log('[Config] Features enabled:', featureManager.getEnabledFeatures().length);

if (ENV.DEBUG_MODE) {
  console.log('[Config] Debug mode enabled');
  console.log('[Config] Enabled features:', featureManager.getEnabledFeatures());
}
