/**
 * Give Command
 * Refactored give command using the new command architecture
 */

import { Slash<PERSON>ommandBuilder, PermissionFlagsBits } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createSuccessEmbed, addUserInfo, formatCoins, EMOJIS } from '../../utils/embedBuilder';
import { adjustBalance } from '../../services/economyService';
import { ValidationError } from '../../utils/errorHandler';
import { VALIDATION } from '../../config/constants';

/**
 * Give command implementation
 */
export class GiveCommand extends BaseCommand {
  constructor() {
    super({
      name: 'give',
      description: 'Give coins to a user (admin only)',
      category: CommandCategory.ADMIN,
      adminOnly: true,
      requiredFeatures: ['ECONOMY_SYSTEM'],
      requiredPermissions: ['Administrator'],
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addUserOption(option =>
        option.setName('user')
          .setDescription('The user to give coins to')
          .setRequired(true))
      .addIntegerOption(option =>
        option.setName('amount')
          .setDescription('Amount of coins to give')
          .setRequired(true)
          .setMinValue(VALIDATION.MIN_TRANSACTION_AMOUNT)
          .setMaxValue(VALIDATION.MAX_TRANSACTION_AMOUNT))
      .addStringOption(option =>
        option.setName('reason')
          .setDescription('Reason for giving coins (optional)')
          .setRequired(false)
          .setMaxLength(VALIDATION.MAX_REASON_LENGTH));
  }

  /**
   * Execute the give command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const targetUser = interaction.options.getUser('user', true);
    const amount = interaction.options.getInteger('amount', true);
    const reason = interaction.options.getString('reason') || 'Administrative action';

    // Validation
    this.validateGive(targetUser.id, amount);

    try {
      // Adjust balance - this will automatically handle user creation with upsert
      await adjustBalance(
        targetUser.id,
        amount,
        'give',
        `Given by admin ${interaction.user.username} (${interaction.user.id}): ${reason}`,
        interaction.client,
        interaction.guild?.id
      );

      // Create rich admin success embed
      const embed = createSuccessEmbed('Coins Awarded Successfully!')
        .setDescription(
          `${EMOJIS.ADMIN.HAMMER} **Administrative Action Completed**\n\n` +
          `${formatCoins(amount)} has been awarded to **${targetUser.displayName}**!`
        )
        .addFields(
          {
            name: `${EMOJIS.ADMIN.KEY} Administrator`,
            value: `**${interaction.user.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.ACTIONS.TARGET} Recipient`,
            value: `**${targetUser.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.ECONOMY.COINS} Amount`,
            value: formatCoins(amount),
            inline: true
          }
        );

      if (reason !== 'Administrative action') {
        embed.addFields({
          name: `${EMOJIS.MISC.SCROLL} Reason`,
          value: reason,
          inline: false
        });
      }

      // Add admin info
      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Admin ${interaction.user.username} gave ${amount} PLC to ${targetUser.username}`, {
        adminId: interaction.user.id,
        recipientId: targetUser.id,
        amount,
        reason,
        guildId: interaction.guild?.id,
      });

      // Try to notify recipient via DM
      try {
        const recipientEmbed = createSuccessEmbed('Coins Received!')
          .setDescription(
            `${EMOJIS.ECONOMY.COINS} You received ${formatCoins(amount)} from an administrator!\n\n` +
            `**Reason:** ${reason}`
          );

        await targetUser.send({ embeds: [recipientEmbed] });
      } catch (error) {
        // DM failed, but give was successful
        this.logger.debug(`Failed to send give notification DM to ${targetUser.username}`, { error });
      }

    } catch (error) {
      this.logger.error('Error executing give command', { 
        error, 
        adminId: interaction.user.id, 
        recipientId: targetUser.id, 
        amount,
        reason 
      });
      throw error;
    }
  }

  /**
   * Validate give parameters
   */
  private validateGive(recipientId: string, amount: number): void {
    if (amount <= 0) {
      throw new ValidationError('Amount must be greater than 0.');
    }

    if (amount > VALIDATION.MAX_TRANSACTION_AMOUNT) {
      throw new ValidationError(`Amount cannot exceed ${formatCoins(VALIDATION.MAX_TRANSACTION_AMOUNT)}.`);
    }
  }
}
