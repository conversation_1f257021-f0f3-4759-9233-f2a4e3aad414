/**
 * Pay Command
 * Refactored pay command using the new command architecture
 */

import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createSuccessEmbed, addUserInfo, formatCoins, EMOJIS } from '../../utils/embedBuilder';
import { adjustBalance, ensureUser } from '../../services/economyService';
import { ValidationError } from '../../utils/errorHandler';
import { VALIDATION } from '../../config/constants';

/**
 * Pay command implementation
 */
export class PayCommand extends BaseCommand {
  constructor() {
    super({
      name: 'pay',
      description: 'Transfer coins to another user',
      category: CommandCategory.ECONOMY,
      requiredFeatures: ['ECONOMY_SYSTEM'],
      cooldown: 5, // Longer cooldown for transfers
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: <PERSON>lash<PERSON>ommandBuilder): void {
    command
      .addUserOption(option =>
        option.setName('user')
          .setDescription('The user to send coins to')
          .setRequired(true))
      .addIntegerOption(option =>
        option.setName('amount')
          .setDescription('Amount of coins to send')
          .setRequired(true)
          .setMinValue(VALIDATION.MIN_TRANSACTION_AMOUNT)
          .setMaxValue(VALIDATION.MAX_TRANSACTION_AMOUNT));
  }

  /**
   * Execute the pay command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const targetUser = interaction.options.getUser('user', true);
    const amount = interaction.options.getInteger('amount', true);

    // Validation
    this.validatePayment(interaction.user.id, targetUser.id, amount);

    try {
      // Get sender's current balance
      const senderUser = await ensureUser(interaction.user.id);
      
      if (senderUser.balance < amount) {
        throw new ValidationError(`Insufficient balance. You have ${formatCoins(senderUser.balance)} but need ${formatCoins(amount)}.`);
      }

      // Ensure recipient exists
      await ensureUser(targetUser.id);

      // Perform the transfer
      await adjustBalance(
        interaction.user.id,
        -amount,
        'pay',
        `Payment to ${targetUser.username} (${targetUser.id})`,
        interaction.client,
        interaction.guild?.id
      );

      await adjustBalance(
        targetUser.id,
        amount,
        'pay',
        `Payment from ${interaction.user.username} (${interaction.user.id})`,
        interaction.client,
        interaction.guild?.id
      );

      // Create success embed
      const embed = createSuccessEmbed('Payment Sent Successfully!')
        .setDescription(
          `${EMOJIS.ECONOMY.TRANSFER} **Payment Completed**\n\n` +
          `${formatCoins(amount)} has been sent to **${targetUser.displayName}**!`
        )
        .addFields(
          {
            name: `${EMOJIS.ACTIONS.SENDER} From`,
            value: `**${interaction.user.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.ACTIONS.TARGET} To`,
            value: `**${targetUser.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.ECONOMY.COINS} Amount`,
            value: formatCoins(amount),
            inline: true
          }
        );

      // Add updated balance info
      const newBalance = senderUser.balance - amount;
      addUserInfo(embed, interaction.user, newBalance);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Payment completed: ${interaction.user.username} sent ${amount} PLC to ${targetUser.username}`);

      // Try to notify recipient via DM
      try {
        const recipientEmbed = createSuccessEmbed('Payment Received!')
          .setDescription(
            `${EMOJIS.ECONOMY.COINS} You received ${formatCoins(amount)} from **${interaction.user.displayName}**!`
          );

        await targetUser.send({ embeds: [recipientEmbed] });
      } catch (error) {
        // DM failed, but payment was successful
        this.logger.debug(`Failed to send payment notification DM to ${targetUser.username}`, { error });
      }

    } catch (error) {
      this.logger.error('Error executing pay command', { 
        error, 
        senderId: interaction.user.id, 
        recipientId: targetUser.id, 
        amount 
      });
      throw error;
    }
  }

  /**
   * Validate payment parameters
   */
  private validatePayment(senderId: string, recipientId: string, amount: number): void {
    if (senderId === recipientId) {
      throw new ValidationError('You cannot send coins to yourself.');
    }

    if (amount <= 0) {
      throw new ValidationError('Amount must be greater than 0.');
    }

    if (amount > VALIDATION.MAX_TRANSACTION_AMOUNT) {
      throw new ValidationError(`Amount cannot exceed ${formatCoins(VALIDATION.MAX_TRANSACTION_AMOUNT)}.`);
    }
  }
}
