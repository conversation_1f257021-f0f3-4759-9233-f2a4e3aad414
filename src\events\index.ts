/**
 * Events Module Index
 * Centralized event handler registration and management
 */

import { IApplicationContext } from '../core/interfaces';
import { BaseEventHandler, eventRegistry } from './base';

// Import all event handlers
import { ReadyEventHandler } from './ready';
import { InteractionCreateEventHandler } from './interactionCreate';
import { MessageCreateEventHandler } from './messageCreate';
import { MessageReactionAddEventHandler } from './messageReactionAdd';
import { GuildMemberAddEventHandler } from './guildMemberAdd';
import { GuildMemberRemoveEventHandler } from './guildMemberRemove';
import { GuildMemberUpdateEventHandler } from './guildMemberUpdate';
import { VoiceStateUpdateEventHandler } from './voiceStateUpdate';

/**
 * Event handler factory
 */
export class EventHandlerFactory {
  /**
   * Create all event handlers
   */
  static createHandlers(app: IApplicationContext): BaseEventHandler[] {
    return [
      new ReadyEventHandler(app),
      new InteractionCreateEventHandler(app),
      new MessageCreateEventHandler(app),
      new MessageReactionAddEventHandler(app),
      new GuildMemberAddEventHandler(app),
      new GuildMemberRemoveEventHandler(app),
      new GuildMemberUpdateEventHandler(app),
      new VoiceStateUpdateEventHandler(app),
    ];
  }
}

/**
 * Event manager for registering and managing event handlers
 */
export class EventManager {
  private app: IApplicationContext;
  private handlers: BaseEventHandler[] = [];

  constructor(app: IApplicationContext) {
    this.app = app;
  }

  /**
   * Initialize and register all event handlers
   */
  initialize(): void {
    // Create all handlers
    this.handlers = EventHandlerFactory.createHandlers(this.app);

    // Register handlers with Discord client
    for (const handler of this.handlers) {
      this.registerHandler(handler);
      eventRegistry.register(handler);
    }

    this.app.logger.info(`[EventManager] Registered ${this.handlers.length} event handlers`);
  }

  /**
   * Register a single event handler with Discord client
   */
  private registerHandler(handler: BaseEventHandler): void {
    if (handler.once) {
      this.app.client.once(handler.name, (...args) => handler.execute(...args));
    } else {
      this.app.client.on(handler.name, (...args) => handler.execute(...args));
    }

    this.app.logger.debug(`[EventManager] Registered handler: ${handler.name} (once: ${handler.once || false})`);
  }

  /**
   * Unregister all event handlers
   */
  shutdown(): void {
    // Remove all listeners
    this.app.client.removeAllListeners();
    
    // Clear registry
    eventRegistry.clear();
    
    this.app.logger.info('[EventManager] All event handlers unregistered');
  }

  /**
   * Get registered handlers
   */
  getHandlers(): BaseEventHandler[] {
    return [...this.handlers];
  }

  /**
   * Get handler by name
   */
  getHandler(name: string): BaseEventHandler | undefined {
    return this.handlers.find(handler => handler.name === name);
  }
}

// Export all event handlers and utilities
export { BaseEventHandler, eventRegistry } from './base';
export { ReadyEventHandler } from './ready';
export { InteractionCreateEventHandler } from './interactionCreate';
export { MessageCreateEventHandler } from './messageCreate';
export { MessageReactionAddEventHandler } from './messageReactionAdd';
export { GuildMemberAddEventHandler } from './guildMemberAdd';
export { GuildMemberRemoveEventHandler } from './guildMemberRemove';
export { GuildMemberUpdateEventHandler } from './guildMemberUpdate';
export { VoiceStateUpdateEventHandler } from './voiceStateUpdate';

export default EventManager;
