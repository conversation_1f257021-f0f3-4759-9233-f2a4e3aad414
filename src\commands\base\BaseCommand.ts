/**
 * Base Command Class
 * Abstract base class for all Discord slash commands
 */

import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { ICommand, ILogger, CommandContext } from '../../core/interfaces';
import { createLogger } from '../../core/logger';
import { withErrorHandler } from '../../utils/errorHandler';
import { isFeatureActive } from '../../config/features';

/**
 * Command categories for organization
 */
export enum CommandCategory {
  ECONOMY = 'economy',
  ADMIN = 'admin',
  ROLE = 'role',
  MILESTONE = 'milestone',
  UTILITY = 'utility',
  AUTOMATION = 'automation',
}

/**
 * Command configuration interface
 */
export interface CommandConfig {
  name: string;
  description: string;
  category: CommandCategory;
  adminOnly?: boolean;
  guildOnly?: boolean;
  cooldown?: number;
  requiredFeatures?: string[];
  requiredPermissions?: (keyof typeof PermissionFlagsBits)[];
}

/**
 * Abstract base command class
 */
export abstract class BaseCommand implements ICommand {
  public readonly data: SlashCommandBuilder;
  public readonly category: string;
  public readonly adminOnly: boolean;
  public readonly guildOnly: boolean;
  public readonly cooldown: number;
  public readonly requiredFeatures: string[];
  
  protected logger: ILogger;
  private config: CommandConfig;

  constructor(config: CommandConfig) {
    this.config = config;
    this.category = config.category;
    this.adminOnly = config.adminOnly || false;
    this.guildOnly = config.guildOnly || true;
    this.cooldown = config.cooldown || 3;
    this.requiredFeatures = config.requiredFeatures || [];
    
    this.logger = createLogger(`command:${config.name}`);
    this.data = this.buildCommand(config);
  }

  /**
   * Build the slash command
   */
  private buildCommand(config: CommandConfig): SlashCommandBuilder {
    const command = new SlashCommandBuilder()
      .setName(config.name)
      .setDescription(config.description);

    // Set default permissions for admin commands
    if (config.adminOnly) {
      command.setDefaultMemberPermissions(PermissionFlagsBits.Administrator);
    }

    // Add required permissions
    if (config.requiredPermissions) {
      const permissions = config.requiredPermissions.reduce((acc, perm) => {
        return acc | PermissionFlagsBits[perm];
      }, 0n);
      command.setDefaultMemberPermissions(permissions);
    }

    // Allow subclasses to customize the command
    this.customizeCommand(command);
    
    return command;
  }

  /**
   * Override in subclasses to customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    // Default implementation - override in subclasses
  }

  /**
   * Execute the command with error handling and validation
   */
  async execute(interaction: ChatInputCommandInteraction): Promise<void> {
    const wrappedExecute = withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
      // Validate execution context
      await this.validateExecution(interaction);
      
      // Create command context
      const context: CommandContext = {
        interaction,
        client: interaction.client,
        guild: interaction.guild,
        member: interaction.member as any,
        logger: this.logger,
      };

      // Log command execution
      this.logExecution(interaction);

      // Execute the command
      await this.executeCommand(context);
    });

    await wrappedExecute(interaction);
  }

  /**
   * Abstract method for command implementation
   */
  protected abstract executeCommand(context: CommandContext): Promise<void>;

  /**
   * Validate command execution requirements
   */
  private async validateExecution(interaction: ChatInputCommandInteraction): Promise<void> {
    // Check if command requires guild context
    if (this.guildOnly && !interaction.guild) {
      throw new Error('This command can only be used in a server.');
    }

    // Check required features
    for (const feature of this.requiredFeatures) {
      if (!isFeatureActive(feature)) {
        throw new Error(`This command requires the ${feature} feature to be enabled.`);
      }
    }

    // Check admin permissions
    if (this.adminOnly && interaction.member) {
      const member = interaction.member as any;
      if (!member.permissions?.has(PermissionFlagsBits.Administrator)) {
        throw new Error('This command requires administrator permissions.');
      }
    }
  }

  /**
   * Log command execution
   */
  private logExecution(interaction: ChatInputCommandInteraction): void {
    this.logger.info(`Command executed: ${this.config.name}`, {
      userId: interaction.user.id,
      username: interaction.user.username,
      guildId: interaction.guild?.id,
      guildName: interaction.guild?.name,
      channelId: interaction.channel?.id,
      commandName: this.config.name,
      category: this.category,
    });
  }

  /**
   * Check if a feature is enabled
   */
  protected isFeatureEnabled(featureName: string): boolean {
    return isFeatureActive(featureName);
  }

  /**
   * Get command configuration
   */
  public getConfig(): CommandConfig {
    return { ...this.config };
  }

  /**
   * Get command metadata
   */
  public getMetadata() {
    return {
      name: this.config.name,
      description: this.config.description,
      category: this.category,
      adminOnly: this.adminOnly,
      guildOnly: this.guildOnly,
      cooldown: this.cooldown,
      requiredFeatures: this.requiredFeatures,
    };
  }
}

/**
 * Command registry for managing command instances
 */
export class CommandRegistry {
  private commands = new Map<string, BaseCommand>();
  private categories = new Map<CommandCategory, BaseCommand[]>();
  private logger: ILogger;

  constructor() {
    this.logger = createLogger('command-registry');
  }

  /**
   * Register a command
   */
  register(command: BaseCommand): void {
    if (this.commands.has(command.data.name)) {
      this.logger.warn(`Command already registered: ${command.data.name}`);
      return;
    }

    this.commands.set(command.data.name, command);
    
    // Add to category
    const category = command.category as CommandCategory;
    if (!this.categories.has(category)) {
      this.categories.set(category, []);
    }
    this.categories.get(category)!.push(command);

    this.logger.debug(`Registered command: ${command.data.name} (${command.category})`);
  }

  /**
   * Get command by name
   */
  get(name: string): BaseCommand | undefined {
    return this.commands.get(name);
  }

  /**
   * Get all commands
   */
  getAll(): BaseCommand[] {
    return Array.from(this.commands.values());
  }

  /**
   * Get commands by category
   */
  getByCategory(category: CommandCategory): BaseCommand[] {
    return this.categories.get(category) || [];
  }

  /**
   * Get all categories
   */
  getCategories(): CommandCategory[] {
    return Array.from(this.categories.keys());
  }

  /**
   * Get command count
   */
  getCount(): number {
    return this.commands.size;
  }

  /**
   * Clear all commands
   */
  clear(): void {
    this.commands.clear();
    this.categories.clear();
    this.logger.debug('Cleared all commands');
  }
}

/**
 * Global command registry instance
 */
export const commandRegistry = new CommandRegistry();
