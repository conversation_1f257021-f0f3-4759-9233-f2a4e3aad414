/**
 * Formatting Utilities
 * Centralized formatting functions for consistent display
 */

import { User, GuildMember } from 'discord.js';
import { ECONOMY } from '../../config/constants';

/**
 * Currency formatting utilities
 */
export class CurrencyFormatter {
  /**
   * Format coins with currency symbol and proper formatting
   */
  static formatCoins(amount: number): string {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return `0 ${ECONOMY.CURRENCY_SYMBOL}`;
    }

    // Add thousand separators for large amounts
    const formattedAmount = amount.toLocaleString();
    return `${formattedAmount} ${ECONOMY.CURRENCY_SYMBOL}`;
  }

  /**
   * Format coins with emoji
   */
  static formatCoinsWithEmoji(amount: number): string {
    return `${ECONOMY.CURRENCY_EMOJI} ${this.formatCoins(amount)}`;
  }

  /**
   * Format percentage
   */
  static formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * Format large numbers with suffixes (K, M, B)
   */
  static formatLargeNumber(num: number): string {
    if (num < 1000) return num.toString();
    if (num < 1000000) return `${(num / 1000).toFixed(1)}K`;
    if (num < 1000000000) return `${(num / 1000000).toFixed(1)}M`;
    return `${(num / 1000000000).toFixed(1)}B`;
  }
}

/**
 * Time formatting utilities
 */
export class TimeFormatter {
  /**
   * Format duration in milliseconds to human readable
   */
  static formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  /**
   * Format timestamp to relative time
   */
  static formatRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  }

  /**
   * Format date to Discord timestamp
   */
  static formatDiscordTimestamp(date: Date, style: 't' | 'T' | 'd' | 'D' | 'f' | 'F' | 'R' = 'f'): string {
    const timestamp = Math.floor(date.getTime() / 1000);
    return `<t:${timestamp}:${style}>`;
  }
}

/**
 * User formatting utilities
 */
export class UserFormatter {
  /**
   * Format user display name without mentions
   */
  static formatDisplayName(user: User | GuildMember): string {
    if ('displayName' in user) {
      return user.displayName;
    }
    return user.username;
  }

  /**
   * Format user tag (username#discriminator)
   */
  static formatUserTag(user: User): string {
    return user.tag;
  }

  /**
   * Format user mention (for intentional pings)
   */
  static formatUserMention(user: User): string {
    return `<@${user.id}>`;
  }

  /**
   * Format user info for embeds
   */
  static formatUserInfo(user: User, includeId: boolean = false): string {
    let info = `**${user.username}**`;
    if (includeId) {
      info += `\nID: \`${user.id}\``;
    }
    return info;
  }
}

/**
 * List formatting utilities
 */
export class ListFormatter {
  /**
   * Format array as numbered list
   */
  static formatNumberedList(items: string[], startIndex: number = 1): string {
    return items.map((item, index) => `${startIndex + index}. ${item}`).join('\n');
  }

  /**
   * Format array as bulleted list
   */
  static formatBulletedList(items: string[], bullet: string = '•'): string {
    return items.map(item => `${bullet} ${item}`).join('\n');
  }

  /**
   * Format leaderboard entries
   */
  static formatLeaderboard(entries: Array<{ rank: number; name: string; value: string }>): string {
    return entries.map(entry => {
      const medal = this.getRankMedal(entry.rank);
      return `${medal} **${entry.rank}.** ${entry.name} - ${entry.value}`;
    }).join('\n');
  }

  /**
   * Get medal emoji for rank
   */
  private static getRankMedal(rank: number): string {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return '🏅';
    }
  }

  /**
   * Format key-value pairs
   */
  static formatKeyValuePairs(pairs: Record<string, string>, separator: string = ': '): string {
    return Object.entries(pairs)
      .map(([key, value]) => `**${key}**${separator}${value}`)
      .join('\n');
  }
}

/**
 * Text formatting utilities
 */
export class TextFormatter {
  /**
   * Truncate text with ellipsis
   */
  static truncate(text: string, maxLength: number, suffix: string = '...'): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
  }

  /**
   * Capitalize first letter
   */
  static capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }

  /**
   * Convert to title case
   */
  static toTitleCase(text: string): string {
    return text.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }

  /**
   * Format code block
   */
  static formatCodeBlock(code: string, language: string = ''): string {
    return `\`\`\`${language}\n${code}\n\`\`\``;
  }

  /**
   * Format inline code
   */
  static formatInlineCode(code: string): string {
    return `\`${code}\``;
  }

  /**
   * Format bold text
   */
  static formatBold(text: string): string {
    return `**${text}**`;
  }

  /**
   * Format italic text
   */
  static formatItalic(text: string): string {
    return `*${text}*`;
  }

  /**
   * Format strikethrough text
   */
  static formatStrikethrough(text: string): string {
    return `~~${text}~~`;
  }

  /**
   * Format spoiler text
   */
  static formatSpoiler(text: string): string {
    return `||${text}||`;
  }
}

/**
 * Progress formatting utilities
 */
export class ProgressFormatter {
  /**
   * Create progress bar
   */
  static createProgressBar(
    current: number, 
    max: number, 
    length: number = 10, 
    filledChar: string = '█', 
    emptyChar: string = '░'
  ): string {
    const percentage = Math.max(0, Math.min(1, current / max));
    const filledLength = Math.round(length * percentage);
    const emptyLength = length - filledLength;
    
    return filledChar.repeat(filledLength) + emptyChar.repeat(emptyLength);
  }

  /**
   * Format progress with percentage
   */
  static formatProgress(current: number, max: number): string {
    const percentage = Math.max(0, Math.min(100, (current / max) * 100));
    const progressBar = this.createProgressBar(current, max);
    return `${progressBar} ${percentage.toFixed(1)}%`;
  }
}

/**
 * Comprehensive formatting utility
 */
export class FormatUtils {
  static currency = CurrencyFormatter;
  static time = TimeFormatter;
  static user = UserFormatter;
  static list = ListFormatter;
  static text = TextFormatter;
  static progress = ProgressFormatter;

  /**
   * Format coins (shorthand)
   */
  static formatCoins = CurrencyFormatter.formatCoins;

  /**
   * Format user display name (shorthand)
   */
  static formatDisplayName = UserFormatter.formatDisplayName;

  /**
   * Format relative time (shorthand)
   */
  static formatRelativeTime = TimeFormatter.formatRelativeTime;
}

export default FormatUtils;
