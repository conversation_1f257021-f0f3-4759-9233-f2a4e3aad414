/**
 * Validation Utilities
 * Centralized validation functions with type safety
 */

import { ValidationError } from '../errorHandler';
import { VALIDATION } from '../../config/constants';

/**
 * Discord ID validation
 */
export class DiscordValidator {
  /**
   * Validate Discord ID format
   */
  static validateDiscordId(id: string): void {
    if (!id || typeof id !== 'string') {
      throw new ValidationError('Discord ID must be a non-empty string');
    }

    const trimmedId = id.trim();
    if (!trimmedId) {
      throw new ValidationError('Discord ID cannot be empty');
    }

    if (!VALIDATION.DISCORD_ID_REGEX.test(trimmedId)) {
      throw new ValidationError('Invalid Discord ID format');
    }
  }

  /**
   * Validate and sanitize Discord ID
   */
  static sanitizeDiscordId(id: string): string {
    this.validateDiscordId(id);
    return id.trim();
  }
}

/**
 * Amount validation for transactions
 */
export class AmountValidator {
  /**
   * Validate transaction amount
   */
  static validateAmount(amount: number, context?: string): void {
    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new ValidationError(`Invalid amount: must be a number${context ? ` for ${context}` : ''}`);
    }

    if (amount < VALIDATION.MIN_TRANSACTION_AMOUNT) {
      throw new ValidationError(`Amount must be at least ${VALIDATION.MIN_TRANSACTION_AMOUNT}${context ? ` for ${context}` : ''}`);
    }

    if (amount > VALIDATION.MAX_TRANSACTION_AMOUNT) {
      throw new ValidationError(`Amount cannot exceed ${VALIDATION.MAX_TRANSACTION_AMOUNT}${context ? ` for ${context}` : ''}`);
    }

    if (!Number.isInteger(amount)) {
      throw new ValidationError(`Amount must be a whole number${context ? ` for ${context}` : ''}`);
    }
  }

  /**
   * Validate positive amount
   */
  static validatePositiveAmount(amount: number, context?: string): void {
    this.validateAmount(amount, context);
    
    if (amount <= 0) {
      throw new ValidationError(`Amount must be greater than 0${context ? ` for ${context}` : ''}`);
    }
  }

  /**
   * Validate balance sufficiency
   */
  static validateSufficientBalance(currentBalance: number, requiredAmount: number, context?: string): void {
    if (currentBalance < requiredAmount) {
      throw new ValidationError(
        `Insufficient balance${context ? ` for ${context}` : ''}. ` +
        `Required: ${requiredAmount}, Available: ${currentBalance}`
      );
    }
  }
}

/**
 * Text validation
 */
export class TextValidator {
  /**
   * Validate text length
   */
  static validateLength(text: string, maxLength: number, fieldName: string): void {
    if (!text) {
      throw new ValidationError(`${fieldName} cannot be empty`);
    }

    if (text.length > maxLength) {
      throw new ValidationError(`${fieldName} cannot exceed ${maxLength} characters`);
    }
  }

  /**
   * Validate description text
   */
  static validateDescription(description: string): void {
    this.validateLength(description, VALIDATION.MAX_DESCRIPTION_LENGTH, 'Description');
  }

  /**
   * Validate reason text
   */
  static validateReason(reason: string): void {
    this.validateLength(reason, VALIDATION.MAX_REASON_LENGTH, 'Reason');
  }

  /**
   * Validate name text
   */
  static validateName(name: string): void {
    this.validateLength(name, VALIDATION.MAX_NAME_LENGTH, 'Name');
  }

  /**
   * Sanitize text input
   */
  static sanitizeText(text: string): string {
    return text.trim().replace(/\s+/g, ' ');
  }
}

/**
 * Permission validation
 */
export class PermissionValidator {
  /**
   * Validate admin permissions
   */
  static validateAdminPermissions(member: any): void {
    if (!member) {
      throw new ValidationError('Member information not available');
    }

    if (!member.permissions?.has('Administrator')) {
      throw new ValidationError('This command requires administrator permissions');
    }
  }

  /**
   * Validate guild context
   */
  static validateGuildContext(guild: any): void {
    if (!guild) {
      throw new ValidationError('This command can only be used in a server');
    }
  }

  /**
   * Validate user permissions for role management
   */
  static validateRoleManagementPermissions(member: any): void {
    if (!member) {
      throw new ValidationError('Member information not available');
    }

    const hasPermission = member.permissions?.has('Administrator') || 
                         member.permissions?.has('ManageRoles');

    if (!hasPermission) {
      throw new ValidationError('This command requires Administrator or Manage Roles permissions');
    }
  }
}

/**
 * Feature validation
 */
export class FeatureValidator {
  /**
   * Validate feature availability
   */
  static validateFeature(featureName: string, isEnabled: boolean): void {
    if (!isEnabled) {
      throw new ValidationError(`The ${featureName} feature is not enabled on this server`);
    }
  }

  /**
   * Validate multiple features
   */
  static validateFeatures(features: Array<{ name: string; enabled: boolean }>): void {
    const disabledFeatures = features.filter(f => !f.enabled).map(f => f.name);
    
    if (disabledFeatures.length > 0) {
      throw new ValidationError(
        `The following required features are not enabled: ${disabledFeatures.join(', ')}`
      );
    }
  }
}

/**
 * Rate limiting validation
 */
export class RateLimitValidator {
  private static lastUsage = new Map<string, number>();

  /**
   * Validate rate limit
   */
  static validateRateLimit(userId: string, commandName: string, cooldownSeconds: number): void {
    const key = `${userId}:${commandName}`;
    const now = Date.now();
    const lastUsed = this.lastUsage.get(key) || 0;
    const timeSinceLastUse = now - lastUsed;
    const cooldownMs = cooldownSeconds * 1000;

    if (timeSinceLastUse < cooldownMs) {
      const remainingSeconds = Math.ceil((cooldownMs - timeSinceLastUse) / 1000);
      throw new ValidationError(
        `Please wait ${remainingSeconds} second(s) before using this command again`
      );
    }

    this.lastUsage.set(key, now);
  }

  /**
   * Clear rate limit for user/command
   */
  static clearRateLimit(userId: string, commandName: string): void {
    const key = `${userId}:${commandName}`;
    this.lastUsage.delete(key);
  }

  /**
   * Clear all rate limits
   */
  static clearAllRateLimits(): void {
    this.lastUsage.clear();
  }
}

/**
 * Comprehensive validation utility
 */
export class ValidationUtils {
  static discord = DiscordValidator;
  static amount = AmountValidator;
  static text = TextValidator;
  static permission = PermissionValidator;
  static feature = FeatureValidator;
  static rateLimit = RateLimitValidator;

  /**
   * Validate common command parameters
   */
  static validateCommandParams(params: {
    userId?: string;
    amount?: number;
    text?: string;
    adminRequired?: boolean;
    member?: any;
    guild?: any;
    features?: string[];
  }): void {
    if (params.userId) {
      this.discord.validateDiscordId(params.userId);
    }

    if (params.amount !== undefined) {
      this.amount.validatePositiveAmount(params.amount);
    }

    if (params.text) {
      this.text.validateDescription(params.text);
    }

    if (params.adminRequired && params.member) {
      this.permission.validateAdminPermissions(params.member);
    }

    if (params.guild) {
      this.permission.validateGuildContext(params.guild);
    }

    if (params.features) {
      // This would need to be implemented with actual feature checking
      // this.feature.validateFeatures(params.features.map(f => ({ name: f, enabled: isFeatureActive(f) })));
    }
  }
}

export default ValidationUtils;
