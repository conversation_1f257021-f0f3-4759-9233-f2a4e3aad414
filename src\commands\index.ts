/**
 * Commands Module Index
 * Centralized exports for the command system
 */

// Base command system
export { BaseCommand, CommandCategory, CommandRegistry, commandRegistry } from './base/BaseCommand';

// Command manager
export { CommandManager, commandManager } from './CommandManager';

// Feature-based command groups
export * from './economy';
export * from './admin';

// Legacy command compatibility
export const legacyCommands = {
  // Economy commands
  balance: require('./balance'),
  pay: require('./pay'),
  history: require('./history'),
  leaderboard: require('./leaderboard'),
  
  // Admin commands
  give: require('./give'),
  fine: require('./fine'),
  announce: require('./announce'),
  tax: require('./tax'),
  starterbalance: require('./starterbalance'),
  testcleanup: require('./testcleanup'),
  incomecredentials: require('./incomecredentials'),
  
  // Role commands
  roles: require('./roles'),
  addrole: require('./addrole'),
  editrole: require('./editrole'),
  removerole: require('./removerole'),
  richestrole: require('./richestrole'),
  
  // Milestone commands
  milestone: require('./milestone'),
  milestones: require('./milestones'),
  milestonestatus: require('./milestonestatus'),
  
  // Utility commands
  help: require('./help'),
  placeholders: require('./placeholders'),
  
  // Automation commands
  automessage: require('./automessage'),
  editautomessage: require('./editautomessage'),
  monetizechannel: require('./monetizechannel'),
};

/**
 * Initialize command system
 */
export async function initializeCommands(): Promise<any> {
  const { commandManager } = await import('./CommandManager');
  return await commandManager.loadCommands();
}

/**
 * Get all commands for Discord client
 */
export function getDiscordCommands(): any {
  const { commandManager } = require('./CommandManager');
  return commandManager.getDiscordCommands();
}
