/**
 * Guild Member Add Event Handler
 * Handles Discord guild member join events for automated messages and milestone tracking
 */

import { GuildMember } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';

/**
 * Guild member add event handler
 */
export class GuildMemberAddEventHandler extends BaseEventHandler {
  public readonly name = 'guildMemberAdd';

  constructor(app: IApplicationContext) {
    super(app);
  }

  /**
   * Execute guild member add event
   */
  async execute(member: GuildMember): Promise<void> {
    try {
      this.logExecution(`Member joined: ${member.displayName}`, {
        userId: member.user.id,
        guildId: member.guild.id,
        guildName: member.guild.name,
      });

      // Process join messages
      if (this.isFeatureEnabled('AUTO_MESSAGES')) {
        await this.processJoinMessages(member);
      }

      // Track login activity for milestones (new member joining counts as login)
      if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
        await this.trackJoinActivity(member);
      }

      // Note: Starter balance is processed in guildMemberUpdate when roles are added

    } catch (error) {
      this.handleError(error, {
        userId: member.user.id,
        guildId: member.guild.id,
        displayName: member.displayName,
      });
    }
  }

  /**
   * Process automated join messages
   */
  private async processJoinMessages(member: GuildMember): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { processJoinMessage } = await import('../services/automessageService');

      const joinResult = await processJoinMessage(member);
      if (joinResult.sent) {
        this.logger.info(`[GuildMemberAdd] Sent ${joinResult.templatesProcessed} join message(s) to ${member.displayName} in ${member.guild.name}`);
      }
      if (joinResult.errors.length > 0) {
        this.logger.error(`[GuildMemberAdd] Errors processing join messages for ${member.displayName}`, {
          errors: joinResult.errors,
        });
      }
    } catch (error) {
      this.logger.error('[GuildMemberAdd] Error processing join messages', {
        error,
        userId: member.user.id,
        guildId: member.guild.id,
      });
    }
  }

  /**
   * Track join activity for milestones
   */
  private async trackJoinActivity(member: GuildMember): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { checkAndProcessMilestones } = await import('../services/milestoneService');

      const milestoneResults = await checkAndProcessMilestones(
        this.app.client,
        member.user.id,
        member.guild.id,
        'login',
        { timestamp: new Date() }
      );

      if (milestoneResults.length > 0) {
        this.logger.info(`[GuildMemberAdd] New member ${member.displayName} achieved ${milestoneResults.length} milestone(s) on join`);
      }
    } catch (error) {
      this.logger.error('[GuildMemberAdd] Error processing join milestones', {
        error,
        userId: member.user.id,
        guildId: member.guild.id,
      });
    }
  }
}
