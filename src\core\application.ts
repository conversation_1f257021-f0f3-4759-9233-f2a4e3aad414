/**
 * Application Bootstrap and Context
 * Main application class that manages services, client, and lifecycle
 */

import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { IApplicationContext, IService, ILogger, ServiceOptions } from './interfaces';
import { DatabaseService } from './database';
import { Logger, getLogger } from './logger';
import { getDiscordConfig, isFeatureActive } from '../config';

/**
 * Service registration information
 */
interface ServiceRegistration {
  service: IService;
  options: ServiceOptions;
  initialized: boolean;
}

/**
 * Main application class
 */
export class Application implements IApplicationContext {
  public readonly client: Client;
  public readonly logger: ILogger;
  public readonly services = new Map<string, IService>();
  
  private serviceRegistrations = new Map<string, ServiceRegistration>();
  private isShuttingDown = false;

  constructor() {
    this.logger = getLogger();
    this.client = this.createDiscordClient();
    
    // Setup graceful shutdown
    this.setupGracefulShutdown();
    
    this.logger.info('[Application] Application instance created');
  }

  /**
   * Create Discord client with proper configuration
   */
  private createDiscordClient(): Client {
    const config = getDiscordConfig();
    
    // Convert string intents to GatewayIntentBits
    const intents = config.intents.map(intent => {
      switch (intent) {
        case 'Guilds': return GatewayIntentBits.Guilds;
        case 'GuildMessages': return GatewayIntentBits.GuildMessages;
        case 'MessageContent': return GatewayIntentBits.MessageContent;
        case 'GuildMembers': return GatewayIntentBits.GuildMembers;
        case 'GuildMessageReactions': return GatewayIntentBits.GuildMessageReactions;
        default: throw new Error(`Unknown intent: ${intent}`);
      }
    });

    const client = new Client({ intents });
    
    // Add commands collection
    (client as any).commands = new Collection();
    
    return client;
  }

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('[Application] Initializing application...');
      
      // Register core services
      await this.registerCoreServices();
      
      // Initialize services in dependency order
      await this.initializeServices();
      
      // Connect to Discord
      await this.connectToDiscord();
      
      this.logger.info('[Application] Application initialized successfully');
    } catch (error) {
      this.logger.error('[Application] Failed to initialize application', { error });
      throw error;
    }
  }

  /**
   * Register core services
   */
  private async registerCoreServices(): Promise<void> {
    // Database service
    const databaseService = new DatabaseService(this.logger);
    this.registerService(databaseService, { autoStart: true, priority: 1 });
  }

  /**
   * Register a service
   */
  registerService(service: IService, options: ServiceOptions = {}): void {
    const defaultOptions: ServiceOptions = {
      autoStart: true,
      dependencies: [],
      priority: 10,
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    this.serviceRegistrations.set(service.name, {
      service,
      options: finalOptions,
      initialized: false,
    });
    
    this.services.set(service.name, service);
    
    this.logger.debug(`[Application] Registered service: ${service.name}`, {
      autoStart: finalOptions.autoStart,
      dependencies: finalOptions.dependencies,
      priority: finalOptions.priority,
    });
  }

  /**
   * Get a service by name
   */
  getService<T extends IService>(name: string): T {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service not found: ${name}`);
    }
    return service as T;
  }

  /**
   * Initialize services in dependency order
   */
  private async initializeServices(): Promise<void> {
    // Sort services by priority
    const sortedServices = Array.from(this.serviceRegistrations.entries())
      .sort(([, a], [, b]) => (a.options.priority || 10) - (b.options.priority || 10));

    for (const [name, registration] of sortedServices) {
      if (!registration.options.autoStart) {
        continue;
      }

      await this.initializeService(name, registration);
    }
  }

  /**
   * Initialize a single service
   */
  private async initializeService(name: string, registration: ServiceRegistration): Promise<void> {
    if (registration.initialized) {
      return;
    }

    // Check dependencies
    if (registration.options.dependencies) {
      for (const dependency of registration.options.dependencies) {
        const depRegistration = this.serviceRegistrations.get(dependency);
        if (!depRegistration) {
          throw new Error(`Dependency not found: ${dependency} for service ${name}`);
        }
        
        if (!depRegistration.initialized) {
          await this.initializeService(dependency, depRegistration);
        }
      }
    }

    try {
      this.logger.info(`[Application] Initializing service: ${name}`);
      
      if (registration.service.initialize) {
        await registration.service.initialize();
      }
      
      registration.initialized = true;
      this.logger.info(`[Application] Service initialized: ${name}`);
    } catch (error) {
      this.logger.error(`[Application] Failed to initialize service: ${name}`, { error });
      throw error;
    }
  }

  /**
   * Connect to Discord
   */
  private async connectToDiscord(): Promise<void> {
    const config = getDiscordConfig();
    
    this.logger.info('[Application] Connecting to Discord...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Discord connection timeout'));
      }, 30000);

      this.client.once('ready', () => {
        clearTimeout(timeout);
        this.logger.info(`[Application] Connected to Discord as ${this.client.user?.tag}`);
        resolve();
      });

      this.client.login(config.token).catch(reject);
    });
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
    
    signals.forEach(signal => {
      process.on(signal, async () => {
        this.logger.info(`[Application] Received ${signal}, shutting down gracefully...`);
        await this.shutdown();
        process.exit(0);
      });
    });

    process.on('uncaughtException', (error) => {
      this.logger.error('[Application] Uncaught exception', { error });
      this.shutdown().then(() => process.exit(1));
    });

    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('[Application] Unhandled rejection', { reason, promise });
    });
  }

  /**
   * Shutdown the application
   */
  async shutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }
    
    this.isShuttingDown = true;
    this.logger.info('[Application] Shutting down application...');

    try {
      // Shutdown Discord client
      if (this.client.isReady()) {
        this.client.destroy();
        this.logger.info('[Application] Discord client destroyed');
      }

      // Shutdown services in reverse order
      const sortedServices = Array.from(this.serviceRegistrations.entries())
        .sort(([, a], [, b]) => (b.options.priority || 10) - (a.options.priority || 10));

      for (const [name, registration] of sortedServices) {
        if (registration.initialized && registration.service.shutdown) {
          try {
            this.logger.info(`[Application] Shutting down service: ${name}`);
            await registration.service.shutdown();
            registration.initialized = false;
          } catch (error) {
            this.logger.error(`[Application] Error shutting down service: ${name}`, { error });
          }
        }
      }

      this.logger.info('[Application] Application shutdown complete');
    } catch (error) {
      this.logger.error('[Application] Error during shutdown', { error });
    }
  }

  /**
   * Get application health status
   */
  async getHealthStatus(): Promise<any> {
    const status = {
      application: 'healthy',
      discord: this.client.isReady() ? 'connected' : 'disconnected',
      services: {} as Record<string, string>,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };

    // Check service health
    for (const [name, registration] of this.serviceRegistrations.entries()) {
      if (registration.initialized) {
        status.services[name] = 'initialized';
      } else {
        status.services[name] = 'not_initialized';
      }
    }

    return status;
  }
}

/**
 * Global application instance
 */
let globalApp: Application | null = null;

/**
 * Get or create global application instance
 */
export function getApplication(): Application {
  if (!globalApp) {
    globalApp = new Application();
  }
  return globalApp;
}

/**
 * Initialize and start the application
 */
export async function startApplication(): Promise<Application> {
  const app = getApplication();
  await app.initialize();
  return app;
}

export default Application;
