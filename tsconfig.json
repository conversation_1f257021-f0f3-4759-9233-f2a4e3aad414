{"compilerOptions": {"target": "ES2021", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "baseUrl": "./src", "paths": {"@/*": ["*"], "@config/*": ["config/*"], "@core/*": ["core/*"], "@services/*": ["services/*"], "@events/*": ["events/*"], "@commands/*": ["commands/*"], "@models/*": ["models/*"], "@utils/*": ["utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}