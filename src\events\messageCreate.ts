/**
 * Message Create Event Handler
 * Handles Discord message creation events for mention reactions and milestone tracking
 */

import { Message } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';

/**
 * Message create event handler
 */
export class MessageCreateEventHandler extends BaseEventHandler {
  public readonly name = 'messageCreate';

  constructor(app: IApplicationContext) {
    super(app);
  }

  /**
   * Execute message create event
   */
  async execute(message: Message): Promise<void> {
    try {
      // Ignore bot messages and DMs
      if (message.author.bot || !message.guild) return;

      // Handle bot mentions
      await this.handleBotMention(message);

      // Track message activity for milestones
      if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
        await this.trackMessageActivity(message);
      }

    } catch (error) {
      this.handleError(error, {
        messageId: message.id,
        channelId: message.channel.id,
        guildId: message.guild?.id,
        userId: message.author.id,
      });
    }
  }

  /**
   * Handle bot mentions with reaction
   */
  private async handleBotMention(message: Message): Promise<void> {
    try {
      // Check if bot is mentioned
      const botMentioned = message.mentions.has(this.app.client.user!);
      if (botMentioned) {
        // React with checkmark emoji
        await message.react('✅');
        
        this.logExecution('Bot mentioned, reacted with checkmark', {
          messageId: message.id,
          channelId: message.channel.id,
          userId: message.author.id,
        });
      }
    } catch (error) {
      this.logger.error('[MessageCreate] Failed to react to bot mention', {
        error,
        messageId: message.id,
        channelId: message.channel.id,
      });
    }
  }

  /**
   * Track message activity for milestones
   */
  private async trackMessageActivity(message: Message): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { checkAndProcessMilestones } = await import('../services/milestoneService');

      const milestoneResults = await checkAndProcessMilestones(
        this.app.client,
        message.author.id,
        message.guild!.id,
        'message',
        {
          channelId: message.channel.id,
          messageContent: message.content,
          messageLength: message.content.length,
          timestamp: message.createdAt
        }
      );

      if (milestoneResults.length > 0) {
        this.logger.info(`[MessageCreate] User ${message.author.tag} achieved ${milestoneResults.length} milestone(s) from message activity`);
      }
    } catch (error) {
      this.logger.error('[MessageCreate] Error processing message milestones', {
        error,
        messageId: message.id,
        userId: message.author.id,
        guildId: message.guild?.id,
      });
    }
  }
}
