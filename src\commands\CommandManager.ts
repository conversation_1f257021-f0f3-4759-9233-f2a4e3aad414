/**
 * Command Manager
 * Centralized command loading and management system
 */

import { Collection } from 'discord.js';
import fs from 'fs';
import path from 'path';
import { BaseCommand, CommandRegistry, commandRegistry, CommandCategory } from './base/BaseCommand';
import { ILogger } from '../core/interfaces';
import { createLogger } from '../core/logger';

// Import new command classes
import { BalanceCommand } from './economy/BalanceCommand';
import { PayCommand } from './economy/PayCommand';
import { GiveCommand } from './admin/GiveCommand';

/**
 * Command loading statistics
 */
interface CommandLoadStats {
  totalLoaded: number;
  newArchitecture: number;
  legacyCommands: number;
  failedLoads: number;
  categories: Record<CommandCategory, number>;
}

/**
 * Command manager for loading and organizing commands
 */
export class CommandManager {
  private logger: ILogger;
  private registry: CommandRegistry;
  private discordCommands: Collection<string, any>;

  constructor() {
    this.logger = createLogger('command-manager');
    this.registry = commandRegistry;
    this.discordCommands = new Collection();
  }

  /**
   * Load all commands (both new and legacy)
   */
  async loadCommands(): Promise<CommandLoadStats> {
    this.logger.info('[CommandManager] Loading commands...');

    const stats: CommandLoadStats = {
      totalLoaded: 0,
      newArchitecture: 0,
      legacyCommands: 0,
      failedLoads: 0,
      categories: {
        [CommandCategory.ECONOMY]: 0,
        [CommandCategory.ADMIN]: 0,
        [CommandCategory.ROLE]: 0,
        [CommandCategory.MILESTONE]: 0,
        [CommandCategory.UTILITY]: 0,
        [CommandCategory.AUTOMATION]: 0,
      },
    };

    // Load new architecture commands
    await this.loadNewCommands(stats);

    // Load legacy commands
    await this.loadLegacyCommands(stats);

    this.logger.info(`[CommandManager] Command loading complete`, {
      totalLoaded: stats.totalLoaded,
      newArchitecture: stats.newArchitecture,
      legacyCommands: stats.legacyCommands,
      failedLoads: stats.failedLoads,
      categories: stats.categories,
    });

    return stats;
  }

  /**
   * Load new architecture commands
   */
  private async loadNewCommands(stats: CommandLoadStats): Promise<void> {
    const newCommands: BaseCommand[] = [
      new BalanceCommand(),
      new PayCommand(),
      new GiveCommand(),
    ];

    for (const command of newCommands) {
      try {
        this.registry.register(command);
        this.discordCommands.set(command.data.name, command);
        
        stats.newArchitecture++;
        stats.totalLoaded++;
        stats.categories[command.category as CommandCategory]++;

        this.logger.debug(`[CommandManager] Loaded new command: ${command.data.name}`);
      } catch (error) {
        this.logger.error(`[CommandManager] Failed to load new command: ${command.data.name}`, { error });
        stats.failedLoads++;
      }
    }
  }

  /**
   * Load legacy commands from files
   */
  private async loadLegacyCommands(stats: CommandLoadStats): Promise<void> {
    const commandsPath = path.join(__dirname);
    const commandFiles = this.getCommandFiles(commandsPath);

    // Skip files that are already handled by new architecture
    const skipFiles = new Set([
      'balance.ts', 'balance.js',
      'pay.ts', 'pay.js',
      'give.ts', 'give.js',
    ]);

    for (const file of commandFiles) {
      if (skipFiles.has(path.basename(file))) {
        this.logger.debug(`[CommandManager] Skipping ${file} (handled by new architecture)`);
        continue;
      }

      try {
        const command = require(file);
        
        if (command.data && command.execute) {
          this.discordCommands.set(command.data.name, command);
          
          stats.legacyCommands++;
          stats.totalLoaded++;

          // Try to categorize legacy commands
          const category = this.categorizeLegacyCommand(command.data.name);
          if (category) {
            stats.categories[category]++;
          }

          this.logger.debug(`[CommandManager] Loaded legacy command: ${command.data.name}`);
        } else {
          this.logger.warn(`[CommandManager] Invalid command file: ${file}`);
        }
      } catch (error) {
        this.logger.error(`[CommandManager] Failed to load legacy command: ${file}`, { error });
        stats.failedLoads++;
      }
    }
  }

  /**
   * Get all command files recursively
   */
  private getCommandFiles(dir: string): string[] {
    const files: string[] = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip certain directories
          if (['base', 'economy', 'admin', 'role', 'milestone', 'utility', 'automation'].includes(item)) {
            continue;
          }
          files.push(...this.getCommandFiles(fullPath));
        } else if (item.endsWith('.ts') || item.endsWith('.js')) {
          // Skip index files and manager files
          if (!item.includes('index') && !item.includes('Manager') && !item.includes('Base')) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      this.logger.error(`[CommandManager] Error reading directory: ${dir}`, { error });
    }
    
    return files;
  }

  /**
   * Categorize legacy commands based on name
   */
  private categorizeLegacyCommand(commandName: string): CommandCategory | null {
    const economyCommands = ['history', 'leaderboard'];
    const adminCommands = ['fine', 'announce', 'tax', 'starterbalance', 'testcleanup', 'incomecredentials'];
    const roleCommands = ['roles', 'addrole', 'editrole', 'removerole', 'richestrole'];
    const milestoneCommands = ['milestone', 'milestones', 'milestonestatus'];
    const utilityCommands = ['help', 'placeholders'];
    const automationCommands = ['automessage', 'editautomessage', 'monetizechannel'];

    if (economyCommands.includes(commandName)) return CommandCategory.ECONOMY;
    if (adminCommands.includes(commandName)) return CommandCategory.ADMIN;
    if (roleCommands.includes(commandName)) return CommandCategory.ROLE;
    if (milestoneCommands.includes(commandName)) return CommandCategory.MILESTONE;
    if (utilityCommands.includes(commandName)) return CommandCategory.UTILITY;
    if (automationCommands.includes(commandName)) return CommandCategory.AUTOMATION;

    return null;
  }

  /**
   * Get Discord commands collection
   */
  getDiscordCommands(): Collection<string, any> {
    return this.discordCommands;
  }

  /**
   * Get command registry
   */
  getRegistry(): CommandRegistry {
    return this.registry;
  }

  /**
   * Get command by name
   */
  getCommand(name: string): any {
    return this.discordCommands.get(name);
  }

  /**
   * Get commands by category
   */
  getCommandsByCategory(category: CommandCategory): BaseCommand[] {
    return this.registry.getByCategory(category);
  }

  /**
   * Get command statistics
   */
  getStats(): any {
    return {
      totalCommands: this.discordCommands.size,
      newArchitectureCommands: this.registry.getCount(),
      categories: this.registry.getCategories().map(cat => ({
        category: cat,
        count: this.registry.getByCategory(cat).length,
      })),
    };
  }

  /**
   * Clear all commands
   */
  clear(): void {
    this.discordCommands.clear();
    this.registry.clear();
    this.logger.debug('[CommandManager] Cleared all commands');
  }
}

/**
 * Global command manager instance
 */
export const commandManager = new CommandManager();

export default CommandManager;
