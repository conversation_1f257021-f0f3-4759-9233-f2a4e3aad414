/**
 * Discord Utilities
 * Helper functions for Discord-specific operations
 */

import { 
  Client, 
  Guild, 
  GuildMember, 
  User, 
  Role, 
  TextChannel, 
  EmbedBuilder, 
  ButtonBuilder, 
  ActionRowBuilder,
  ButtonStyle,
  PermissionFlagsBits
} from 'discord.js';
import { ValidationError } from '../errorHandler';
import { DISCORD } from '../../config/constants';

/**
 * Guild utilities
 */
export class GuildUtils {
  /**
   * Safely fetch guild by ID
   */
  static async fetchGuild(client: Client, guildId: string): Promise<Guild | null> {
    try {
      return await client.guilds.fetch(guildId);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if guild exists and bot has access
   */
  static async validateGuildAccess(client: Client, guildId: string): Promise<Guild> {
    const guild = await this.fetchGuild(client, guildId);
    if (!guild) {
      throw new ValidationError(`Guild not found or bot doesn't have access: ${guildId}`);
    }
    return guild;
  }

  /**
   * Get guild member count
   */
  static async getMemberCount(guild: Guild): Promise<number> {
    try {
      await guild.members.fetch();
      return guild.memberCount;
    } catch (error) {
      return guild.memberCount; // Fallback to cached count
    }
  }

  /**
   * Check if guild has specific features
   */
  static hasFeature(guild: Guild, feature: string): boolean {
    return guild.features.includes(feature as any);
  }
}

/**
 * Member utilities
 */
export class MemberUtils {
  /**
   * Safely fetch member by ID
   */
  static async fetchMember(guild: Guild, userId: string): Promise<GuildMember | null> {
    try {
      return await guild.members.fetch(userId);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if member exists in guild
   */
  static async validateMemberAccess(guild: Guild, userId: string): Promise<GuildMember> {
    const member = await this.fetchMember(guild, userId);
    if (!member) {
      throw new ValidationError(`Member not found in guild: ${userId}`);
    }
    return member;
  }

  /**
   * Check if member has specific permission
   */
  static hasPermission(member: GuildMember, permission: keyof typeof PermissionFlagsBits): boolean {
    return member.permissions.has(PermissionFlagsBits[permission]);
  }

  /**
   * Check if member has any of the specified permissions
   */
  static hasAnyPermission(member: GuildMember, permissions: (keyof typeof PermissionFlagsBits)[]): boolean {
    return permissions.some(permission => this.hasPermission(member, permission));
  }

  /**
   * Check if member has admin permissions
   */
  static isAdmin(member: GuildMember): boolean {
    return this.hasPermission(member, 'Administrator');
  }

  /**
   * Get member's highest role
   */
  static getHighestRole(member: GuildMember): Role | null {
    return member.roles.highest;
  }

  /**
   * Check if member can manage another member
   */
  static canManageMember(manager: GuildMember, target: GuildMember): boolean {
    if (manager.id === target.id) return false;
    if (this.isAdmin(manager)) return true;
    
    const managerHighest = this.getHighestRole(manager);
    const targetHighest = this.getHighestRole(target);
    
    if (!managerHighest || !targetHighest) return false;
    
    return managerHighest.position > targetHighest.position;
  }
}

/**
 * Role utilities
 */
export class RoleUtils {
  /**
   * Safely fetch role by ID
   */
  static async fetchRole(guild: Guild, roleId: string): Promise<Role | null> {
    try {
      return await guild.roles.fetch(roleId);
    } catch (error) {
      return null;
    }
  }

  /**
   * Find role by name (case-insensitive)
   */
  static findRoleByName(guild: Guild, roleName: string): Role | null {
    return guild.roles.cache.find(role => 
      role.name.toLowerCase() === roleName.toLowerCase()
    ) || null;
  }

  /**
   * Check if role can be managed by bot
   */
  static canManageRole(guild: Guild, role: Role): boolean {
    const botMember = guild.members.me;
    if (!botMember) return false;
    
    const botHighestRole = MemberUtils.getHighestRole(botMember);
    if (!botHighestRole) return false;
    
    return botHighestRole.position > role.position;
  }

  /**
   * Get manageable roles for bot
   */
  static getManageableRoles(guild: Guild): Role[] {
    return guild.roles.cache.filter(role => 
      this.canManageRole(guild, role) && role.name !== '@everyone'
    ).map(role => role);
  }
}

/**
 * Channel utilities
 */
export class ChannelUtils {
  /**
   * Safely fetch channel by ID
   */
  static async fetchChannel(client: Client, channelId: string): Promise<TextChannel | null> {
    try {
      const channel = await client.channels.fetch(channelId);
      return channel?.isTextBased() ? channel as TextChannel : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if bot can send messages in channel
   */
  static canSendMessages(channel: TextChannel): boolean {
    const permissions = channel.permissionsFor(channel.guild.members.me!);
    return permissions?.has([PermissionFlagsBits.SendMessages, PermissionFlagsBits.ViewChannel]) || false;
  }

  /**
   * Check if bot can embed links in channel
   */
  static canEmbedLinks(channel: TextChannel): boolean {
    const permissions = channel.permissionsFor(channel.guild.members.me!);
    return permissions?.has(PermissionFlagsBits.EmbedLinks) || false;
  }

  /**
   * Send message with error handling
   */
  static async safeSendMessage(channel: TextChannel, content: any): Promise<boolean> {
    try {
      if (!this.canSendMessages(channel)) {
        return false;
      }
      
      await channel.send(content);
      return true;
    } catch (error) {
      return false;
    }
  }
}

/**
 * Embed utilities
 */
export class EmbedUtils {
  /**
   * Validate embed limits
   */
  static validateEmbed(embed: EmbedBuilder): void {
    const data = embed.toJSON();
    
    if (data.title && data.title.length > 256) {
      throw new ValidationError('Embed title cannot exceed 256 characters');
    }
    
    if (data.description && data.description.length > DISCORD.MAX_EMBED_DESCRIPTION_LENGTH) {
      throw new ValidationError(`Embed description cannot exceed ${DISCORD.MAX_EMBED_DESCRIPTION_LENGTH} characters`);
    }
    
    if (data.fields && data.fields.length > DISCORD.MAX_EMBED_FIELDS) {
      throw new ValidationError(`Embed cannot have more than ${DISCORD.MAX_EMBED_FIELDS} fields`);
    }
    
    const totalLength = (data.title?.length || 0) + 
                       (data.description?.length || 0) + 
                       (data.fields?.reduce((sum, field) => sum + field.name.length + field.value.length, 0) || 0);
    
    if (totalLength > 6000) {
      throw new ValidationError('Total embed content cannot exceed 6000 characters');
    }
  }

  /**
   * Truncate embed field value if too long
   */
  static truncateFieldValue(value: string, maxLength: number = 1024): string {
    if (value.length <= maxLength) return value;
    return value.substring(0, maxLength - 3) + '...';
  }

  /**
   * Split long text into multiple embed fields
   */
  static splitIntoFields(text: string, fieldName: string, maxLength: number = 1024): Array<{ name: string; value: string }> {
    const fields: Array<{ name: string; value: string }> = [];
    
    if (text.length <= maxLength) {
      fields.push({ name: fieldName, value: text });
      return fields;
    }
    
    let currentText = text;
    let partNumber = 1;
    
    while (currentText.length > 0) {
      let chunk = currentText.substring(0, maxLength);
      
      // Try to break at a newline
      if (currentText.length > maxLength) {
        const lastNewline = chunk.lastIndexOf('\n');
        if (lastNewline > maxLength * 0.5) {
          chunk = chunk.substring(0, lastNewline);
        }
      }
      
      fields.push({
        name: partNumber === 1 ? fieldName : `${fieldName} (${partNumber})`,
        value: chunk
      });
      
      currentText = currentText.substring(chunk.length);
      partNumber++;
    }
    
    return fields;
  }
}

/**
 * Button utilities
 */
export class ButtonUtils {
  /**
   * Create action row with buttons
   */
  static createActionRow(buttons: ButtonBuilder[]): ActionRowBuilder<ButtonBuilder> {
    if (buttons.length > 5) {
      throw new ValidationError('Action row cannot have more than 5 buttons');
    }
    
    return new ActionRowBuilder<ButtonBuilder>().addComponents(buttons);
  }

  /**
   * Create simple button
   */
  static createButton(
    customId: string, 
    label: string, 
    style: ButtonStyle = ButtonStyle.Primary,
    emoji?: string
  ): ButtonBuilder {
    if (label.length > DISCORD.MAX_BUTTON_LABEL_LENGTH) {
      throw new ValidationError(`Button label cannot exceed ${DISCORD.MAX_BUTTON_LABEL_LENGTH} characters`);
    }
    
    const button = new ButtonBuilder()
      .setCustomId(customId)
      .setLabel(label)
      .setStyle(style);
    
    if (emoji) {
      button.setEmoji(emoji);
    }
    
    return button;
  }

  /**
   * Create link button
   */
  static createLinkButton(url: string, label: string, emoji?: string): ButtonBuilder {
    const button = new ButtonBuilder()
      .setURL(url)
      .setLabel(label)
      .setStyle(ButtonStyle.Link);
    
    if (emoji) {
      button.setEmoji(emoji);
    }
    
    return button;
  }
}

/**
 * Comprehensive Discord utility
 */
export class DiscordUtils {
  static guild = GuildUtils;
  static member = MemberUtils;
  static role = RoleUtils;
  static channel = ChannelUtils;
  static embed = EmbedUtils;
  static button = ButtonUtils;

  /**
   * Parse Discord ID from mention or return as-is
   */
  static parseId(input: string): string {
    const match = input.match(/^<[@#&]!?(\d+)>$/);
    return match ? match[1] : input;
  }

  /**
   * Check if string is a Discord ID
   */
  static isDiscordId(input: string): boolean {
    return /^\d{17,20}$/.test(input);
  }

  /**
   * Format user mention
   */
  static formatUserMention(userId: string): string {
    return `<@${userId}>`;
  }

  /**
   * Format role mention
   */
  static formatRoleMention(roleId: string): string {
    return `<@&${roleId}>`;
  }

  /**
   * Format channel mention
   */
  static formatChannelMention(channelId: string): string {
    return `<#${channelId}>`;
  }
}

export default DiscordUtils;
