# Economy Bot - Refactored Architecture

## Overview

The Economy Bot has been completely refactored to implement a professional, modular architecture that follows Node.js and Discord.js best practices. This document outlines the new structure and how to work with it.

## Architecture Principles

### 1. **Separation of Concerns**
- Business logic separated from Discord interactions
- Database operations isolated in dedicated services
- Configuration centralized and type-safe
- Event handling modularized

### 2. **Dependency Injection**
- Services receive dependencies through constructor injection
- Application context provides service registry
- Loose coupling between modules

### 3. **Feature Modularity**
- Each feature can be independently enabled/disabled
- Runtime feature toggles with dependency resolution
- Clean feature boundaries

### 4. **Type Safety**
- Comprehensive TypeScript interfaces
- Strict type checking enabled
- Generic service interfaces

## Directory Structure

```
src/
├── config/                 # Configuration management
│   ├── constants.ts        # Application constants
│   ├── environment.ts      # Environment variables
│   ├── features.ts         # Feature toggle system
│   └── index.ts           # Configuration exports
├── core/                   # Core infrastructure
│   ├── application.ts      # Main application class
│   ├── database.ts         # Database service
│   ├── interfaces.ts       # Core interfaces
│   ├── logger.ts          # Logging system
│   └── index.ts           # Core exports
├── events/                 # Discord event handlers
│   ├── base.ts            # Base event handler
│   ├── ready.ts           # Ready event
│   ├── interactionCreate.ts # Command/button interactions
│   ├── messageCreate.ts    # Message events
│   ├── messageReactionAdd.ts # Reaction events
│   ├── guildMemberAdd.ts   # Member join events
│   ├── guildMemberRemove.ts # Member leave events
│   ├── guildMemberUpdate.ts # Member update events
│   ├── voiceStateUpdate.ts # Voice events
│   └── index.ts           # Event manager
├── services/               # Business logic services
│   ├── base/              # Base service classes
│   │   └── BaseService.ts # Abstract service base
│   ├── economy/           # Economy system
│   │   ├── EconomyService.ts # Main economy service
│   │   └── index.ts       # Economy exports
│   ├── role/              # Role management
│   │   ├── RoleService.ts # Role assignment service
│   │   └── index.ts       # Role exports
│   └── [legacy services]  # Existing services (maintained for compatibility)
├── commands/              # Discord slash commands
├── models/                # Database models
├── utils/                 # Utility functions
├── main.ts               # New main entry point
└── index.ts              # Legacy entry point (maintained)
```

## Core Components

### Application Context (`src/core/application.ts`)

The main application class that manages:
- Discord client lifecycle
- Service registration and dependency injection
- Graceful shutdown handling
- Health monitoring

```typescript
const app = await startApplication();
app.registerService(new EconomyService(app));
const economyService = app.getService<EconomyService>('EconomyService');
```

### Configuration System (`src/config/`)

Centralized configuration with:
- Environment variable validation
- Feature toggle management
- Type-safe constants
- Runtime configuration

```typescript
import { ENV, isFeatureActive, ECONOMY } from './config';

if (isFeatureActive('MILESTONE_SYSTEM')) {
  // Feature is enabled
}
```

### Service Layer (`src/services/`)

Modular services with:
- Base service class with common functionality
- Dependency injection support
- Feature-based organization
- Consistent error handling

```typescript
export class EconomyService extends BaseService implements IEconomyService {
  async adjustBalance(discordId: string, amount: number, type: TransactionType) {
    // Implementation with automatic role checking
  }
}
```

### Event System (`src/events/`)

Modular event handlers with:
- Base event handler class
- Feature-aware processing
- Consistent error handling
- Centralized event management

```typescript
export class MessageCreateEventHandler extends BaseEventHandler {
  async execute(message: Message) {
    // Handle message events with feature checks
  }
}
```

## Feature Toggle System

### Configuration

Features are configured in `src/config/features.ts`:

```typescript
export const FEATURE_REGISTRY: Record<string, FeatureConfig> = {
  ECONOMY_SYSTEM: {
    enabled: true,
    description: 'Core PLC economy system',
    dependencies: [],
  },
  MILESTONE_SYSTEM: {
    enabled: true,
    description: 'Automated milestone rewards',
    dependencies: ['ECONOMY_SYSTEM'],
  },
  // ...
};
```

### Usage

```typescript
import { isFeatureActive, requireFeature } from './config/features';

// Check if feature is enabled
if (isFeatureActive('MILESTONE_SYSTEM')) {
  // Process milestones
}

// Decorator for automatic feature checking
@requireFeature('ECONOMY_SYSTEM')
async adjustBalance() {
  // Method only executes if feature is enabled
}
```

## Migration Guide

### Running the Refactored Version

1. **New Entry Point**: Use `src/main.ts` instead of `src/index.ts`
   ```bash
   npm run dev        # Development with new architecture
   npm run start      # Production with new architecture
   ```

2. **Legacy Support**: The old entry point is maintained for compatibility
   ```bash
   npm run dev:legacy    # Development with legacy architecture
   npm run start:legacy  # Production with legacy architecture
   ```

### Service Usage

```typescript
// Old way
import { adjustBalance } from './services/economyService';
await adjustBalance(userId, amount, 'give');

// New way
const economyService = app.getService<EconomyService>('EconomyService');
await economyService.adjustBalance(userId, amount, 'give');

// Backward compatibility maintained
import { adjustBalance } from './services/economy';
await adjustBalance(userId, amount, 'give'); // Still works
```

## Development Workflow

### Adding New Features

1. **Create Service**: Extend `BaseService` for business logic
2. **Add Configuration**: Update feature registry if needed
3. **Create Events**: Add event handlers if required
4. **Add Commands**: Create slash commands using the service
5. **Register**: Register service in `main.ts`

### Example: Adding a New Service

```typescript
// src/services/newfeature/NewFeatureService.ts
export class NewFeatureService extends BaseService {
  public readonly name = 'NewFeatureService';
  
  @requireFeature('NEW_FEATURE')
  async doSomething() {
    this.logOperation('Doing something');
    // Implementation
  }
}

// src/main.ts
const newFeatureService = new NewFeatureService(app);
app.registerService(newFeatureService, {
  autoStart: true,
  dependencies: ['DatabaseService'],
  priority: 5
});
```

## Benefits of the New Architecture

1. **Maintainability**: Clear separation of concerns and modular structure
2. **Scalability**: Easy to add new features without affecting existing code
3. **Testability**: Services can be easily mocked and tested in isolation
4. **Type Safety**: Comprehensive TypeScript interfaces prevent runtime errors
5. **Feature Management**: Runtime feature toggles for easy deployment control
6. **Error Handling**: Consistent error handling and logging across all modules
7. **Performance**: Optimized service initialization and dependency management

## Backward Compatibility

The refactored architecture maintains full backward compatibility:
- All existing commands continue to work
- Legacy service imports are maintained
- Database schema remains unchanged
- Configuration is backward compatible
- Deployment process unchanged

## Next Steps

1. **Gradual Migration**: Services can be migrated one at a time
2. **Testing**: Comprehensive testing of the new architecture
3. **Documentation**: Update command documentation for new features
4. **Monitoring**: Enhanced logging and monitoring capabilities
5. **Performance**: Optimize service initialization and memory usage
