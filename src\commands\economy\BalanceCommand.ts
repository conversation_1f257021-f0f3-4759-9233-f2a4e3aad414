/**
 * Balance Command
 * Refactored balance command using the new command architecture
 */

import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createEconomyEmbed, addUserInfo, formatCoins, createQuickActionButtons, EMOJIS } from '../../utils/embedBuilder';
import User from '../../models/User';
import { ensureUser } from '../../services/economyService';

/**
 * Balance command implementation
 */
export class BalanceCommand extends BaseCommand {
  constructor() {
    super({
      name: 'balance',
      description: 'Check your Phalanx Loyalty Coin balance',
      category: CommandCategory.ECONOMY,
      requiredFeatures: ['ECONOMY_SYSTEM'],
    });
  }

  /**
   * Execute the balance command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const discordId = interaction.user.id;

    try {
      // Use ensureUser helper to handle user creation/fetch
      const user = await ensureUser(discordId);

      // Check if user has Dynasty membership (if Dynasty system is enabled)
      let dynastyInfo = null;
      if (this.isFeatureEnabled('DYNASTY_SYSTEM')) {
        try {
          const { getDynastyMemberInfo } = await import('../../services/dynastyService');
          dynastyInfo = await getDynastyMemberInfo(discordId);
        } catch (error) {
          // Dynasty service might not be available, continue without it
          this.logger.debug('Dynasty service not available', { error });
        }
      }

      // Create balance embed
      const embed = createEconomyEmbed('Your Balance')
        .setDescription(`${EMOJIS.ECONOMY.COINS} **${formatCoins(user.balance)}**`);

      // Add Dynasty information if available
      if (dynastyInfo && dynastyInfo.dynasty) {
        embed.addFields({
          name: `${EMOJIS.SUCCESS.CROWN} Dynasty`,
          value: `**${dynastyInfo.dynasty.name}**\nLevel ${dynastyInfo.dynasty.level} • ${dynastyInfo.dynasty.memberCount} members`,
          inline: true
        });

        if (dynastyInfo.dynasty.bankBalance > 0) {
          embed.addFields({
            name: `${EMOJIS.ECONOMY.BANK} Dynasty Bank`,
            value: formatCoins(dynastyInfo.dynasty.bankBalance),
            inline: true
          });
        }
      }

      // Add user info
      addUserInfo(embed, interaction.user, user.balance);

      // Add quick action buttons
      const buttons = createQuickActionButtons();

      await interaction.reply({
        embeds: [embed],
        components: [buttons],
        ephemeral: false
      });

      this.logger.info(`Balance checked for user ${interaction.user.username}: ${user.balance} PLC`);

    } catch (error) {
      this.logger.error('Error executing balance command', { error, userId: discordId });
      throw error;
    }
  }
}
