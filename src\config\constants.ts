/**
 * Application Constants
 * Centralized configuration for all constant values used throughout the application
 */

// Economy System Constants
export const ECONOMY = {
  // Currency
  CURRENCY_NAME: 'Phalanx Loyalty Coins',
  CURRENCY_SYMBOL: 'PLC',
  CURRENCY_EMOJI: '🪙',
  
  // Reaction Rewards
  REACTION_REWARD_AMOUNT: 5,
  REACTION_RATE_LIMIT_SECONDS: 30,
  REACTION_MESSAGE_AGE_LIMIT_HOURS: 24,
  
  // Tax System
  TAX_COLLECTION_INTERVAL_HOURS: 1,
  
  // Starter Balance
  DEFAULT_STARTER_BALANCE: 0,
} as const;

// Milestone System Constants
export const MILESTONES = {
  // Rate Limiting
  MAX_WEEKLY_MILESTONE_REWARDS: 50,
  MAX_DAILY_MILESTONE_REWARDS: 10,
  
  // Anti-Exploitation
  SUSPICIOUS_ACTIVITY_THRESHOLD: 5,
  BLACKLIST_DURATION_HOURS: 24,
  
  // Default Rewards
  DEFAULT_LOGIN_STREAK_REWARD: 10,
  DEFAULT_MESSAGE_COUNT_REWARD: 15,
  DEFAULT_VOICE_TIME_REWARD: 20,
  
  // Diminishing Returns
  DEFAULT_DIMINISHING_FACTOR: 0.9,
} as const;

// Dynasty System Constants
export const DYNASTY = {
  // Requirements
  MIN_PLC_REQUIREMENT: 5000,
  MIN_TENURE_DAYS: 30,
  
  // Economic Benefits
  MILESTONE_BONUS_PERCENTAGE: 10,
  
  // Progression
  MAX_LEVEL: 10,
  BASE_LEVEL_REQUIREMENT: 1000,
  LEVEL_MULTIPLIER: 1.5,
} as const;

// Discord Integration Constants
export const DISCORD = {
  // Intents
  REQUIRED_INTENTS: [
    'Guilds',
    'GuildMessages', 
    'MessageContent',
    'GuildMembers',
    'GuildMessageReactions'
  ],
  
  // Permissions
  ADMIN_PERMISSIONS: ['Administrator', 'ManageGuild', 'ManageRoles'],
  
  // Rate Limiting
  COMMAND_COOLDOWN_SECONDS: 3,
  ADMIN_COMMAND_COOLDOWN_SECONDS: 2,
  
  // Limits
  MAX_EMBED_FIELDS: 25,
  MAX_EMBED_DESCRIPTION_LENGTH: 4096,
  MAX_BUTTON_LABEL_LENGTH: 80,
} as const;

// Database Constants
export const DATABASE = {
  // Connection
  CONNECTION_TIMEOUT_MS: 30000,
  OPERATION_TIMEOUT_MS: 10000,
  
  // Cleanup
  USER_CLEANUP_GRACE_PERIOD_MS: 5000,
  
  // Indexing
  REQUIRED_INDEXES: [
    { collection: 'users', index: { discordId: 1 }, unique: true },
    { collection: 'transactions', index: { discordId: 1, timestamp: -1 } },
    { collection: 'reactionrewards', index: { userId: 1, timestamp: -1 } },
    { collection: 'milestoneconfigurations', index: { guildId: 1, milestoneType: 1 }, unique: true },
  ],
} as const;

// Error Handling Constants
export const ERROR_HANDLING = {
  // Error Categories
  CATEGORIES: {
    DATABASE: 'Database Error',
    VALIDATION: 'Validation Error', 
    PERMISSION: 'Permission Error',
    RATE_LIMIT: 'Rate Limit Error',
    NETWORK: 'Network Error',
    UNKNOWN: 'Unknown Error',
  },
  
  // Retry Configuration
  MAX_RETRIES: 3,
  RETRY_DELAY_MS: 1000,
  EXPONENTIAL_BACKOFF: true,
} as const;

// Logging Constants
export const LOGGING = {
  // Levels
  LEVELS: {
    ERROR: 'error',
    WARN: 'warn', 
    INFO: 'info',
    DEBUG: 'debug',
  },
  
  // Categories
  CATEGORIES: {
    ECONOMY: 'economy',
    MILESTONE: 'milestone',
    DYNASTY: 'dynasty',
    ADMIN: 'admin',
    DATABASE: 'database',
    DISCORD: 'discord',
  },
} as const;

// Feature Flags
export const FEATURES = {
  // Core Systems
  ECONOMY_SYSTEM: true,
  MILESTONE_SYSTEM: true,
  DYNASTY_SYSTEM: true,
  REACTION_REWARDS: true,
  
  // Admin Features
  TAX_SYSTEM: true,
  STARTER_BALANCE: true,
  AUTO_MESSAGES: true,
  
  // Advanced Features
  ROLE_AUTOMATION: true,
  USER_CLEANUP: true,
  AUDIT_LOGGING: true,
  
  // Development Features
  DEBUG_MODE: false,
  VERBOSE_LOGGING: false,
} as const;

// Validation Constants
export const VALIDATION = {
  // Discord IDs
  DISCORD_ID_REGEX: /^\d{17,20}$/,
  
  // Amounts
  MIN_TRANSACTION_AMOUNT: 1,
  MAX_TRANSACTION_AMOUNT: 1000000,
  
  // Text Lengths
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_REASON_LENGTH: 200,
  MAX_NAME_LENGTH: 100,
  
  // Limits
  MAX_ROLES_PER_GUILD: 100,
  MAX_USERS_PER_OPERATION: 100,
} as const;

// Cron Job Schedules
export const SCHEDULES = {
  TAX_COLLECTION: '0 * * * *', // Every hour
  MILESTONE_RESET: '0 0 * * *', // Daily at midnight UTC
  USER_CLEANUP: '0 2 * * *', // Daily at 2 AM UTC
  AUDIT_CLEANUP: '0 3 * * 0', // Weekly on Sunday at 3 AM UTC
} as const;
