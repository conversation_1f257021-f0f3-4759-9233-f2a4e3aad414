/**
 * Core Module Index
 * Exports all core infrastructure components
 */

// Application and context
export { Application, getApplication, startApplication } from './application';

// Database service
export { DatabaseService } from './database';

// Logger
export { Logger, CategoryLogger, getLogger, createLogger, loggers, shutdownLogger } from './logger';

// Interfaces and types
export * from './interfaces';

// Re-export commonly used types
export type {
  IService,
  IDatabaseService,
  ILogger,
  ICommand,
  IEventHandler,
  IEconomyService,
  IRoleService,
  IMilestoneService,
  IApplicationContext,
  TransactionType,
  ActivityType,
  LeaderboardEntry,
  TransactionRecord,
  RoleAssignmentResult,
  UserRoleInfo,
  MilestoneCheckResult,
  ErrorContext,
  CommandContext,
  ButtonContext,
} from './interfaces';
